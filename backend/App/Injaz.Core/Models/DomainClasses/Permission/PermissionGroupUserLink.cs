using System;
using System.ComponentModel.DataAnnotations.Schema;
using Injaz.Core.Models.DomainClasses.App;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.Permission;

[Table("permission_group_user_links")]
public class PermissionGroupUserLink : BaseModel
{
    public PermissionGroupUserLink()
    {
        CreationTime = DateTime.UtcNow;
    }

    [Column("group_id", Order = 0)] public Guid GroupId { get; set; }

    [Column("user_id", Order = 1)] public Guid UserId { get; set; }

    [ForeignKey("GroupId")]
    [InverseProperty("UserLinks")]
    public virtual PermissionGroup PermissionGroup { get; set; }

    [ForeignKey("UserId")]
    [InverseProperty("PermissionGroupLinks")]
    public virtual User User { get; set; }
}
