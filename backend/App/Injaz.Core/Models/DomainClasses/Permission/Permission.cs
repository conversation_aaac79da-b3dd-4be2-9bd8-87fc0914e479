using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;

namespace Injaz.Core.Models.DomainClasses.Permission;

[Table("permissions")]
public class Permission
{
    [Key] [Column("id")] [MaxLength(512)] public string Id { get; set; }

    public virtual ICollection<PermissionUserLink> UserLinks { get; set; }
    public virtual ICollection<PermissionPermissionGroupLink> GroupLinks { get; set; }
    public virtual ICollection<PermissionOverrider> Overriders { get; set; }
    public virtual ICollection<PermissionOverrider> OverriddenPermissions { get; set; }
}