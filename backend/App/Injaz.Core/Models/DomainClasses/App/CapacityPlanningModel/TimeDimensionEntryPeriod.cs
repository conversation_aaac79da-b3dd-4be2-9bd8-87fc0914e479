using System;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App.CapacityPlanningModel;

[Table("capacity_data_entry_periods")]
public class TimeDimensionEntryPeriod : ModifiableModel<User>
{
    [Column("department_id")]
    public Guid? DepartmentId { get; set; }

    [Column("time_id")]
    public Guid TimeId { get; set; }

    [Column("is_active_service_channel_demand")]
    public bool IsActiveServiceChannelDemand { get; set; }

    [Column("is_active_center_parameters")]
    public bool IsActiveCenterParameters { get; set; }

    [Column("is_active_center_monthly_demand")]
    public bool IsActiveCenterMonthlyDemand { get; set; }

    [Column("is_active_service_demand")]
    public bool IsActiveServiceDemand { get; set; }

    [ForeignKey(nameof(DepartmentId))]
    [InverseProperty(nameof(App.Department.DataEntryPeriods))]
    public virtual Department Department { get; set; }

    [ForeignKey(nameof(TimeId))]
    [InverseProperty(nameof(TimeDimension.DataEntryPeriods))]
    public virtual TimeDimension Time { get; set; }
}
