using System;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App.CapacityPlanningModel;

[Table("capacity_center_monthly_demands")]
public class CenterMonthlyDemand : ModifiableModel<User>
{
    [Column("department_id")]
    public Guid DepartmentId { get; set; }

    [Column("time_id")]
    public Guid TimeId { get; set; }

    [Column("employees_available")]
    public int EmployeesAvailable { get; set; }

    [Column("fast_services_count")]
    public int FastServicesCount { get; set; }

    [Column("fast_service_time")]
    public double FastServiceTime { get; set; }

    [Column("regular_services_count")]
    public int RegularServicesCount { get; set; }

    [Column("regular_service_time")]
    public double RegularServiceTime { get; set; }

    [Column("complex_services_count")]
    public int ComplexServicesCount { get; set; }

    [Column("complex_service_time")]
    public double ComplexServiceTime { get; set; }

    [Column("daily_work_hours_per_employee")]
    public int DailyWorkHoursPerEmployee { get; set; }

    [Column("total_work_days_per_month")]
    public int WorkDaysPerMonth { get; set; }

    [Column("work_hours_per_day")]
    public int WorkHoursPerDay { get; set; }

    [Column("religious_and_national_occasions_days_per_month")]
    public int ReligiousAndNationalOccasionsDaysPerMonth { get; set; }

    [Column("actual_transactions_per_month")]
    public int ActualTransactionsPerMonth { get; set; }

    [Column("actual_customers_per_month")]
    public int ActualCustomersPerMonth { get; set; }

    [Column("available_seats_in_center")]
    public int AvailableSeatsInCenter { get; set; }

    [Column("available_parking_at_center")]
    public int AvailableParkingAtCenter { get; set; }

    // related tot the services channels
    [Column("happiness_center_volume")]
    public int HappinessCenterVolume { get; set; }

    [Column("moi_app_volume")]
    public int MoiAppVolume { get; set; }

    [Column("ajp_app_volume")]
    public int AjpAppVolume { get; set; }

    [Column("web_site_volume")]
    public int WebsiteVolume { get; set; }

    [ForeignKey(nameof(DepartmentId))]
    [InverseProperty(nameof(App.Department.CenterMonthlyDemands))]
    public virtual Department Department { get; set; }

    [ForeignKey(nameof(TimeId))]
    [InverseProperty(nameof(TimeDimension.CenterMonthlyDemands))]
    public virtual TimeDimension Time { get; set; }
}
