using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App.ServiceModel;

[Table("service_delivery_channels")]
public class ServiceDeliveryChannel : ModifiableModel<User>
{
    [Column("name_ar")] [MaxLength(1024)] public string NameAr { get; set; }

    [Column("name_en")] [MaxLength(1024)] public string NameEn { get; set; }

    public virtual ICollection<ServiceServiceDeliveryChannelLink> ServiceLinks { get; set; }
}