using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App;

[Table("kpi_types")]
public class KpiType : ModifiableModel<User>
{
    [Column("name_ar")] [MaxLength(1024)] public string NameAr { get; set; }

    [Column("name_en")] [MaxLength(1024)] public string NameEn { get; set; }

    [Column("code")] [MaxLength(32)] public string Code { get; set; }

    [Column("is_used_to_compute_goal_achievement")]
    public int IsUsedToComputeGoalAchievement { get; set; }

    [Column("is_considered_in_dashboard")] public int IsConsideredInDashboard { get; set; }

    [Column("is_linkable_to_plans")] public int IsLinkableToPlans { get; set; }

    [Column("is_excluded_from_calculations")]
    public int IsExcludedFromCalculations { get; set; }

    [Column("order")] public int Order { get; set; }

    public virtual ICollection<Kpi> Kpis { get; set; }
}