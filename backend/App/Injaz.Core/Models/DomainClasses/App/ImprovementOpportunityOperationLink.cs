using System;
using System.ComponentModel.DataAnnotations.Schema;

namespace Injaz.Core.Models.DomainClasses.App;

[Table("improvement_opportunity_operation_links")]
public class ImprovementOpportunityOperationLink
{
    [Column("improvement_opportunity_id", Order = 0)] public Guid ImprovementOpportunityId { get; set; }

    [Column("operation_id", Order = 1)] public Guid OperationId { get; set; }


    [ForeignKey("ImprovementOpportunityId")]
    public virtual ImprovementOpportunity ImprovementOpportunity { get; set; }

    [ForeignKey("OperationId")]
    public virtual Operation Operation { get; set; }
}
