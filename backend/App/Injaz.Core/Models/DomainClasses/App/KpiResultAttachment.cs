using System;
using System.ComponentModel.DataAnnotations;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App;

[Table("kpi_result_attachment")]
public class KpiResultAttachment : Model
{
    [Column("name_ar")] [MaxLength(1024)] public string NameAr { get; set; }

    [Column("name_en")] [MaxLength(1024)] public string NameEn { get; set; }

    [Column("file_name")] public string FileName { get; set; }

    [Column("content_type")] public string ContentType { get; set; }

    // Kpi result resource properties
    [Column("kpi_result_id")] public Guid? KpiResultId { get; set; }

    // This field is going to be filled during data entry.
    // Once the response is approved the `KpiResultId` will
    // be filled, and this one is going to be nullified.
    [Column("kpi_result_data_entry_response_id")]
    public Guid? KpiResultDataEntryResponseId { get; set; }

    [Column("period")] public int Period { get; set; }

    [Column("period_type")]
    [MaxLength(32)]
    public string PeriodType { get; set; }

    [ForeignKey("KpiResultId")]
    [InverseProperty("Attachments")]
    public KpiResult KpiResult { get; set; }

    [ForeignKey("KpiResultDataEntryResponseId")]
    [InverseProperty("Attachments")]
    public KpiResultDataEntryResponse KpiResultDataEntryResponse { get; set; }
}
