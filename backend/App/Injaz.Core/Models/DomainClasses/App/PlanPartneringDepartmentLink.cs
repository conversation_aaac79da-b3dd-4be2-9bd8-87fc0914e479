using System;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;
using DepartmentModel = Injaz.Core.Models.DomainClasses.App.Department;
using PlanModel = Injaz.Core.Models.DomainClasses.App.Plan;

namespace Injaz.Core.Models.DomainClasses.App;

[Table("plan_partnering_department_links")]
public class PlanPartneringDepartmentLink : BaseModel
{
    [Column("plan_id", Order = 0)] public Guid PlanId { get; set; }

    [Column("department_id", Order = 1)] public Guid DepartmentId { get; set; }

    [ForeignKey(nameof(PlanId))]
    [InverseProperty(nameof(PlanModel.PartneringDepartmentLinks))]
    public virtual Plan Plan { get; set; }

    [ForeignKey(nameof(DepartmentId))]
    [InverseProperty(nameof(DepartmentModel.PlanLinks))]
    public virtual Department Department { get; set; }
}
