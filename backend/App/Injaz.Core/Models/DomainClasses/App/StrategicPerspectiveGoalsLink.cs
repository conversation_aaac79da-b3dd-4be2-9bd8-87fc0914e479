using System;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App;

[Table("strategic_perspective_goals_links")]
public class StrategicPerspectiveGoalsLink : BaseModel
{

    [Column("goal_id", Order = 0)]
    public Guid?  GoalId { get; set; }

    [Column("perspective_id", Order = 1)]
    public Guid? PerspectiveId { get; set; }

    [ForeignKey("PerspectiveId")]
    public virtual StrategicPerspective StrategicPerspective { get; set; }

    [ForeignKey("GoalId")]
    public virtual StrategicGoal StrategicGoal { get; set; }

}
