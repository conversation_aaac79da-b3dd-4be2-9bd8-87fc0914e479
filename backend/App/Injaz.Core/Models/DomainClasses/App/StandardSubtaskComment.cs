using System;
using System.ComponentModel.DataAnnotations.Schema;
using MNMWebApp.Models.Abstraction;

namespace Injaz.Core.Models.DomainClasses.App;

[Table("standard_subtask_comments")]
public class StandardSubtaskComment : ModifiableModel<User>
{
    [Column("subtask_id")] public Guid SubtaskId { get; set; }

    [Column("content")] public string Content { get; set; }

    [ForeignKey(nameof(SubtaskId))]
    [InverseProperty(nameof(StandardSubtask.Comments))]
    public virtual StandardSubtask Subtask { get; set; }

    [ForeignKey(nameof(CreatedById))] public virtual User CreatedBy { get; set; }
}
