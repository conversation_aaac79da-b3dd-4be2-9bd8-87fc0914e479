using System;
using System.Collections.Generic;
using System.Linq;
using System.Reflection;
using Injaz.Core.Abstraction;
using Injaz.Core.Models.DomainClasses.App;

namespace Injaz.Core.Models.SqlFunctions;

public class YearRangeSqlFunction : ISqlFunction
{
    public string GetName()
    {
        return "fn_year_range";
    }

    public MethodInfo GetBinding()
    {
        return GetType().GetMethod(nameof(Call));
    }

    public IEnumerable<Type> GetDependencies()
    {
        return Array.Empty<Type>();
    }

    public string GetDefinition()
    {
        return $@"
            CREATE OR
            ALTER
                FUNCTION fn_year_range(@from INT, @to INT)
                RETURNS TABLE
                    AS
                    RETURN
                        (
                            SELECT *
                            FROM years
                            WHERE @from <= value
                              AND value < @to
                        )
            ";
    }

    public static IQueryable<Year> Call(int from, int to)
    {
        throw new NotSupportedException();
    }
}
