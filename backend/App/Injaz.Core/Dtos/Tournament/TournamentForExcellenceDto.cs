using System;
using System.Linq;
using System.Linq.Expressions;
using Injaz.Core.Constants;
using LinqKit;

namespace Injaz.Core.Dtos.Tournament;

public class TournamentForExcellenceDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.Tournament, TournamentForExcellenceDto>> Mapper(
        string lang,
        IQueryable<Core.Models.DomainClasses.App.Kpi> kpis,
        IQueryable<Core.Models.DomainClasses.App.Capability> capabilities,
        IQueryable<Core.Models.DomainClasses.App.LibraryFile> libraryFiles
    )
    {
        var kpisExpression = HelperExpression.TournamentKpis(kpis);
        var capabilityExpression = HelperExpression.TournamentCapabilities(capabilities);
        var filesExpression = HelperExpression.TournamentLibraryFiles(libraryFiles);
        var progressExpression = HelperExpression.TournamentProgress();

        return item => new TournamentForExcellenceDto
        {
            Id = item.Id,
            Name = SupportedCultures.LanguageArabic == lang ? item.NameAr : item.NameEn,
            KpiCount = kpisExpression.Invoke(item).Count(),
            CapabilityCount = capabilityExpression.Invoke(item).Count(),
            LibraryFileCount = filesExpression.Invoke(item).Count(),
            Progress = progressExpression.Invoke(item),
            Achieved = 0, // CustomDbFunctions.ObtainTournamentAchieved(item.Id, null)
        };
    }

    public Guid Id { get; set; }

    public string Name { get; set; }

    public double? Progress { get; set; }

    public int KpiCount { get; set; }

    public int CapabilityCount { get; set; }

    public int LibraryFileCount { get; set; }

    public double? Achieved { get; set; }
}
