using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.Department;
using Injaz.Core.Dtos.PlanDtos.Plans;
using Injaz.Core.Dtos.PlanDtos.PlanSubsubtasks;
using Injaz.Core.Dtos.PlanDtos.PlanTasks;
using Injaz.Core.Dtos.Team;
using Injaz.Core.Dtos.User;
using Injaz.Core.Flow.Services;

namespace Injaz.Core.Dtos.PlanDtos.PlanSubtasks;

public class PlanSubtaskGetDto
{
    public static Expression<Func<Injaz.Core.Models.DomainClasses.App.PlanSubtask, PlanSubtaskGetDto>> Mapper(
        string lang,
        Guid userId,
        // bool hasFullAccess,
        IQueryable<Injaz.Core.Models.DomainClasses.App.Department> departments,
        Expression<Func<Injaz.Core.Models.DomainClasses.App.Plan, PlanUserAbility>> abilityExpression
    )
    {
        var departmentExpression = DepartmentSimpleDto.Mapper(lang);
        var teamExpression = TeamSimpleDto.Mapper(lang);
        var userExpression = UserSimpleDto.Mapper(lang);
        var taskExpression = PlanTaskWithPlanDto.Mapper(lang);
        var weightExpression = HelperExpression.PlanSubtaskWeight();
        var progressExpression = HelperExpression.PlanSubtaskProgress();
        var subsubtaskExpression = PlanSubsubtaskDto.Mapper(
            lang,
            userId,
            departments
        );
        var isApprovedExpression = HelperExpression.IsPlanSubtaskApproved();
        // var isPlanEditableExpression = HelperExpression.IsPlanEditable(hasFullAccess);
        // var isInvolvedPredicate = HelperExpression.IsInvolvedWithPlanEntity(
        //     userId,
        //     departments,
        //     hasFullAccess
        // );

        return item => new PlanSubtaskGetDto
        {
            Id = item.Id,
            Name = SupportedCultures.LanguageArabic.Equals(lang) ? item.NameAr : item.NameEn,
            From = item.From,
            To = item.To,
            AssignedDepartment = item.AssignedDepartmentId == null
                ? null
                : departmentExpression.Invoke(item.AssignedDepartment),
            AssignedTeam = item.AssignedTeamId == null ? null : teamExpression.Invoke(item.AssignedTeam),
            AssignedUser = item.AssignedUserId == null ? null : userExpression.Invoke(item.AssignedUser),
            SecondaryAssignedDepartment = item.SecondaryAssignedDepartment == null
                ? null
                : departmentExpression.Invoke(item.SecondaryAssignedDepartment),
            Weight = weightExpression.Invoke(item),
            Progress = progressExpression.Invoke(item),
            IsApproved = isApprovedExpression.Invoke(item),
            IsPlanInitiallyApproved = item.Task.Plan.FlowState == DefaultFlowState.ApprovedFinal,
            IsRepeated = item.IsRepeated == 1,
            // IsPlanEditable = isPlanEditableExpression.Invoke(item.Task.Plan),
            // IsUserInvolvedWithPlan = isInvolvedPredicate.Invoke(item.Task.Plan),
            Task = taskExpression.Invoke(item.Task),
            Subsubtasks = item.Subsubtasks.Select(x => subsubtaskExpression.Invoke(x)).ToList(),
            PlanUserAbility = abilityExpression.Invoke(item.Task.Plan)
        };
    }

    public Guid Id { get; set; }
    public string Name { get; set; }
    public DateTime? From { get; set; }
    public DateTime? To { get; set; }
    public DepartmentSimpleDto AssignedDepartment { get; set; }
    public TeamSimpleDto AssignedTeam { get; set; }
    public UserSimpleDto AssignedUser { get; set; }
    public DepartmentSimpleDto SecondaryAssignedDepartment { get; set; }
    public double? Weight { get; set; }
    public double? Progress { get; set; }
    public bool IsApproved { get; set; }
    public bool IsPlanInitiallyApproved { get; set; }
    public bool IsRepeated { get; set; }

    // public bool IsPlanEditable { get; set; }
    // public bool IsUserInvolvedWithPlan { get; set; }
    public PlanTaskWithPlanDto Task { get; set; }
    public IEnumerable<PlanSubsubtaskDto> Subsubtasks { get; set; }

    public PlanUserAbility PlanUserAbility { get; set; }
}
