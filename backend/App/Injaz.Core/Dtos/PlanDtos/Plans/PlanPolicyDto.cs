using System;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Dtos.Policy;
using Injaz.Core.Models.DomainClasses.App;

namespace Injaz.Core.Dtos.PlanDtos.Plans;

public class PlanPolicyDto
{
    public static Expression<Func<PlanPolicyLink, PlanPolicyDto>> Mapper(string lang) => item => new PlanPolicyDto
    {
        Policy = PolicySimpleDto.Mapper(lang).Invoke(item.Policy),
        HasPlan = item.HasPlan == 1
    };

    public PolicySimpleDto Policy { get; set; }
    public bool HasPlan { get; set; }
}
