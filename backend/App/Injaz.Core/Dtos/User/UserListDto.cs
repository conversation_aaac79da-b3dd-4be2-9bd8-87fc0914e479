using System;
using System.Linq.Expressions;
using Injaz.Core.Constants;

namespace Injaz.Core.Dtos.User;

public class UserListDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.User, UserListDto>> Mapper(string lang)
    {
        return item =>
            new UserListDto
            {
                Id = item.Id,
                Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
                CreationTime = item.CreationTime,
                Email = item.Email,
                Type = item.Type,
                Status = item.Status
            };
    }

    public Guid Id { get; set; }
    public string Name { get; set; }
    public string Email { get; set; }
    public DateTime CreationTime { get; set; }
    public string Type { get; set; }
    public string Status { get; set; }
}
