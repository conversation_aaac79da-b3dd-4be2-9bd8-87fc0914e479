using System;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Dtos.User;
using Injaz.Core.Models.DomainClasses.App;

namespace Injaz.Core.Dtos.StandardTask;

public class StandardSubtaskCommentDto
{
    public static Expression<Func<StandardSubtaskComment, StandardSubtaskCommentDto>> Mapper(
        string lang
    )
    {
        var userExpression = UserSimpleDto.Mapper(lang);

        return item => new StandardSubtaskCommentDto
        {
            Id = item.Id,
            Content = item.Content,
            CreationTime = item.CreationTime,
            CreatedBy = userExpression.Invoke(item.CreatedBy),
        };
    }

    public Guid Id { get; set; }

    public DateTime CreationTime { get; set; }

    public string Content { get; set; }

    public UserSimpleDto CreatedBy { get; set; }
}
