using System;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Dtos.CapabilityType;

namespace Injaz.Core.Dtos.Capability;

public class CapabilityEditDto
{
    public static
        Expression<Func<Core.Models.DomainClasses.App.Capability, CapabilityEditDto>> Mapper(
            string lang)
    {
        var typePredicate = CapabilityTypeSimpleDto.Mapper(lang);

        return item => new CapabilityEditDto
        {
            Id = item.Id,
            NameAr = item.NameAr,
            NameEn = item.NameEn,
            DescriptionAr = item.DescriptionAr,
            DescriptionEn = item.DescriptionEn,
            Year = item.Year,
            Type = typePredicate.Invoke(item.Type)
        };
    }

    public Guid Id { get; set; }

    public string NameAr { get; set; }

    public string NameEn { get; set; }

    public string DescriptionAr { get; set; }

    public string DescriptionEn { get; set; }

    public int Year { get; set; }

    public CapabilityTypeSimpleDto Type { get; set; }
}
