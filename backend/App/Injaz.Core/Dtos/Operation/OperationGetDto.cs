using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.Department;
using Injaz.Core.Dtos.Kpi;
using Injaz.Core.Dtos.LibraryFile;
using Injaz.Core.Dtos.MainOperationOwner;
using Injaz.Core.Dtos.OperationEnhancement;
using Injaz.Core.Dtos.OperationProcedure;
using Injaz.Core.Dtos.OperationRuleAndRegulation;
using Injaz.Core.Dtos.OperationSpecification;
using Injaz.Core.Dtos.OperationUpdateRequest;
using Injaz.Core.Dtos.Partner;
using Injaz.Core.Dtos.Policy;
using Injaz.Core.Dtos.Service;
using Injaz.Core.Dtos.StrategicGoal;
using Injaz.Core.Dtos.SuccessFactor;
using KpiModel = Injaz.Core.Models.DomainClasses.App.Kpi;
using OperationModel = Injaz.Core.Models.DomainClasses.App.Operation;

namespace Injaz.Core.Dtos.Operation;

public class OperationGetDto
{
    public static Expression<Func<OperationModel, OperationGetDto>> Mapper(
        IQueryable<OperationModel> operationQueryable,
        string lang,
        bool canAchievedBeNegative,
        int fromYear = -1,
        int toYear = -1
    )
    {
        fromYear = fromYear == -1 ? DateTime.UtcNow.Year - 2 : fromYear;
        toYear = toYear == -1 ? fromYear + 3 : toYear;

        var kpisLinkExpression = KpiWithResultDto.Mapper(lang, canAchievedBeNegative, fromYear, toYear);
        var partnersLinkExpression = PartnerSimpleDto.Mapper(lang);
        var successFactoriesLinkExpression = SuccessFactorDto.Mapper(lang);
        var goalExpression = StrategicGoalSimpleDto.Mapper(lang);
        var operationExpression = OperationWithLevelDto.Mapper(lang);
        var departmentExpression = DepartmentListDto.Mapper(lang);
        var mainOperationOwnerExpression = MainOperationOwnerSimpleDto.Mapper(lang);
        var operationFormFileExpression = OperationFormFileDto.Mapper(lang);
        var libraryFileExpression = LibraryFileGetDto.Mapper(lang);
        var operationSimpleExpression = OperationWithLevelDto.Mapper(lang);
        var executorExpression = OperationExecutorDto.Mapper();
        var policyExpression = PolicySimpleDto.Mapper(lang);
        var ruleAndRegulationExpression = OperationRuleAndRegulationSimpleDto.Mapper(lang);
        var specificationExpression = OperationSpecificationSimpleDto.Mapper(lang);
        var enhancementsExpression = OperationEnhancementGetDto.Mapper(lang);
        var procedureExpression = OperationProcedureGetDto.Mapper(lang);
        var serviceExpression = ServiceSimpleDto.Mapper(lang);
        var updateRequestExpression = OperationUpdateRequestListDto.Mapper(lang);
        var hasPendingUpdateRequestExpression = HelperExpression.HasPendingUpdateRequest();
        var rejectedUpdateRequestExpression = HelperExpression.RejectedUpdateRequest();

        return item => new OperationGetDto
        {
            Id = item.Id,
            Code = item.Code,
            Version = item.Version,
            Name = lang == SupportedCultures.LanguageEnglish ? item.NameEn : item.NameAr,
            Description = lang == SupportedCultures.LanguageEnglish ? item.DescriptionEn : item.DescriptionAr,
            Output = item.Output == "task" ? lang == SupportedCultures.LanguageArabic ? "مهمة" : item.Output
                : item.Output == "service" ? lang == SupportedCultures.LanguageArabic ? "خدمه" : item.Output
                : "-",
            Level = item.Level,
            SustainabilityImpact = item.SustainabilityImpact,
            Weight = item.Weight,
            Purpose = item.Purpose,
            ParentOperation = item.ParentId == null ? null : operationExpression.Invoke(item.Parent),
            OwnerDepartment = item.OwnerDepartment != null
                ? departmentExpression.Invoke(item.OwnerDepartment)
                : null,
            MainOperationOwner = item.MainOperationOwner != null
                ? mainOperationOwnerExpression.Invoke(item.MainOperationOwner)
                : null,
            Partners = operationQueryable
                .AsExpandable()
                .Where(x => x.Code.StartsWith(item.Code))
                .SelectMany(x => x.PartnerLinks)
                .Select(a => partnersLinkExpression.Invoke(a.Partner))
                .Distinct()
                .ToList(),
            Kpis = item.KpiLinks.Where(x => x.Kpi.Status == KpiModel.StatusActive)
                .Select(a => kpisLinkExpression.Invoke(a.Kpi)).ToList(),
            FormFiles =
                item.FormFileLinks.Select(a => operationFormFileExpression.Invoke(a)).ToList(),
            MainFlowChart = item.MainFlowChartFile != null
                ? libraryFileExpression.Invoke(item.MainFlowChartFile)
                : null,
            Children = item.Children.Select(a => operationSimpleExpression.Invoke(a)).ToList(),

            Outputs = item.Outputs,
            InputType = item.InputType,
            Inputs = item.Inputs,
            SupplierCategory = item.SupplierCategory,
            OutputType = item.OutputType,
            SupplierName = item.SupplierName,
            MinisterialCode = item.MinisterialCode,
            LocalCode = item.LocalCode,
            TechnicalSolutions = item.TechnicalSolutions,
            Terminologies = item.Terminologies,
            Types = item.Types,
            Duration = item.Duration,
            Beneficiaries = item.Beneficiaries,
            DangerNumber = item.DangerNumber,
            HasPendingUpdateRequest = hasPendingUpdateRequestExpression.Invoke(item),
            RejectedUpdateRequest = rejectedUpdateRequestExpression.Invoke(item) == null
                ? null
                : updateRequestExpression.Invoke(rejectedUpdateRequestExpression.Invoke(item)),
            Executors = item.Executors.Select(x => executorExpression.Invoke(x)).ToList(),
            RulesAndRegulations = item.RuleAndRegulationLinks
                .Select(x => ruleAndRegulationExpression.Invoke(x.OperationRuleAndRegulation)).ToList(),
            Policies = item.PolicyLinks.Select(x => policyExpression.Invoke(x.Policy)).ToList(),
            Specifications = item.SpecificationLinks.Select(x => specificationExpression.Invoke(x.Specification))
                .ToList(),
            Enhancements = item.Enhancements.Select(x => enhancementsExpression.Invoke(x)).ToList(),
            Procedures = item.Procedures.Select(x => procedureExpression.Invoke(x)).ToList(),

            SuccessFactors = item
                .SuccessFactorOperationLinks
                .Select(a => successFactoriesLinkExpression.Invoke(a.SuccessFactor))
                .ToList(),
            StrategicGoals = item
                .GoalLinks
                .Select(x => goalExpression.Invoke(x.StrategicGoal))
                .ToList(),
            Services = item
                .ServiceLinks
                .Select(x => serviceExpression.Invoke(x.Service))
                .ToList(),
        };
    }


    public Guid Id { get; set; }
    public string Code { get; set; }
    public string Name { get; set; }
    public int Level { get; set; }
    public string Description { get; set; }
    public string Output { get; set; }
    public string[] SustainabilityImpact { get; set; }
    public string Purpose { get; set; }
    public double? Weight { get; set; }
    public string DangerNumber { get; set; }


    // level 4 operations
    public string Inputs { get; set; }
    public string InputType { get; set; }
    public string Outputs { get; set; }
    public string OutputType { get; set; }
    public string SupplierName { get; set; }
    public string MinisterialCode { get; set; }
    public string LocalCode { get; set; }
    public string Duration { get; set; }
    public string SupplierCategory { get; set; }
    public string TechnicalSolutions { get; set; }
    public string Terminologies { get; set; }
    public string? Version { get; set; }
    public IEnumerable<string> Types { get; set; }
    public bool HasPendingUpdateRequest { get; set; }
    public OperationUpdateRequestListDto? RejectedUpdateRequest { get; set; }

    public LibraryFileGetDto MainFlowChart { get; set; }
    public OperationWithLevelDto ParentOperation { get; set; }
    public DepartmentListDto OwnerDepartment { get; set; }
    public MainOperationOwnerSimpleDto MainOperationOwner { get; set; }
    public IEnumerable<StrategicGoalSimpleDto> StrategicGoals { get; set; }
    public IEnumerable<SuccessFactorDto> SuccessFactors { get; set; }
    public IEnumerable<PartnerSimpleDto> Partners { get; set; }
    public IEnumerable<OperationWithLevelDto> Children { get; set; }
    public IEnumerable<KpiWithResultDto> Kpis { get; set; }
    public IEnumerable<OperationFormFileDto> FormFiles { get; set; }
    public IEnumerable<string> Beneficiaries { get; set; }

    public IEnumerable<OperationExecutorDto> Executors { get; set; }

    public IEnumerable<OperationRuleAndRegulationSimpleDto> RulesAndRegulations { get; set; }

    public IEnumerable<OperationSpecificationSimpleDto> Specifications { get; set; }

    public IEnumerable<PolicySimpleDto> Policies { get; set; }

    public IEnumerable<OperationEnhancementGetDto> Enhancements { get; set; }
    public IEnumerable<OperationProcedureGetDto> Procedures { get; set; }
    public IEnumerable<ServiceSimpleDto> Services { get; set; }
}
