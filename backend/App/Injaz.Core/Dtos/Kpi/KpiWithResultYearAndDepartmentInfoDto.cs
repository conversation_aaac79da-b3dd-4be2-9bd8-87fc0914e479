using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.Department;
using Injaz.Core.Models.SqlFunctions;

namespace Injaz.Core.Dtos.Kpi;

public class KpiWithResultYearAndDepartmentInfoDto
{
    public static Expression<Func<Models.DomainClasses.App.Kpi, KpiWithResultYearAndDepartmentInfoDto>> Mapper(
        string lang,
        int currentYear,
        IQueryable<Models.DomainClasses.App.Department> departments,
        Guid userId,
        bool hasFullAccessPermission
    )
    {
        var departmentExpression = DepartmentSimpleDto.Mapper(lang);
        var permissionExpression = HelperExpression
            .IsInvolvedWithDepartment(userId, departments, true)
            .Or(x => hasFullAccessPermission);

        return item => new KpiWithResultYearAndDepartmentInfoDto
        {
            Id = item.Id,
            Name = SupportedCultures.LanguageArabic == lang ? item.NameAr : item.NameEn,
            YearsInfo = YearRangeSqlFunction.Call(item.CreationYear, currentYear)
                .Select(year =>
                    new KpiResultYearInfoDto
                    {
                        Year = year.Value,
                        DepartmentsInfo = departments
                            .Where(x => permissionExpression.Invoke(x))
                            .Where(x =>
                                x.KpiResults.Any(y => y.Year == year.Value && y.KpiId == item.Id) ||
                                x.KpiLinks.Any(y => y.KpiId == item.Id)
                            )
                            .Select(x => new KpiResultDepartmentInfoDto
                            {
                                Department = departmentExpression.Invoke(x),
                                HasResult = item.Results.Any(y =>
                                    y.Year == year.Value &&
                                    y.KpiId == item.Id &&
                                    y.DepartmentId == x.Id)
                            })
                            .ToList()
                    })
                .ToList()
        };
    }

    public Guid Id { get; set; }

    public string Name { get; set; }

    public IEnumerable<KpiResultYearInfoDto> YearsInfo { get; set; }
}

public class KpiResultYearInfoDto
{
    public int Year { get; set; }

    public IEnumerable<KpiResultDepartmentInfoDto> DepartmentsInfo { get; set; }
}

public class KpiResultDepartmentInfoDto
{
    public bool HasResult { get; set; }

    public DepartmentSimpleDto Department { get; set; }
}
