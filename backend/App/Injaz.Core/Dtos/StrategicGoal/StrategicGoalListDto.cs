using System;
using System.Linq.Expressions;
using Injaz.Core.Constants;

namespace Injaz.Core.Dtos.StrategicGoal;

public class StrategicGoalListDto
{
    public static Expression<Func<Models.DomainClasses.App.StrategicGoal, StrategicGoalListDto>>
        Mapper(string lang)
    {
        return item => new StrategicGoalListDto
        {
            Id = item.Id,
            Code = item.Code,
            Name = lang == SupportedCultures.LanguageEnglish ? item.NameEn : item.NameAr,
            FromYear = item.FromYear,
            ToYear = item.ToYear,
            Order = item.Order,
            Category = item.Category,
            Weight = item.Weight
        };
    }

    public Guid Id { get; set; }
    public string Code { get; set; }
    public string Name { get; set; }
    public int? Order { get; set; }
    public int? ToYear { get; set; }
    public int? FromYear { get; set; }
    public string Category { get; set; }
    public double? Weight { get; set; }
}
