using System;
using System.Linq.Expressions;
using Injaz.Core.Constants;

namespace Injaz.Core.Dtos.Team;

public class TeamSimpleDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.Team, TeamSimpleDto>> Mapper(string lang) => item => new TeamSimpleDto
    {
        Id = item.Id,
        Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
    };

    public Guid Id { get; set; }
    public string Name { get; set; }
}
