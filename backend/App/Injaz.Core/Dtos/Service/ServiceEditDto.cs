using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Dtos.Department;
using Injaz.Core.Dtos.ServiceCategory;
using Injaz.Core.Dtos.ServiceClientCategory;
using Injaz.Core.Dtos.ServiceDeliveryChannel;
using Injaz.Core.Dtos.ServicePartner;
using Injaz.Core.Dtos.ServiceProviderChannel;
using Injaz.Core.Models.Misc;
using ServiceModel = Injaz.Core.Models.DomainClasses.App.ServiceModel.Service;

namespace Injaz.Core.Dtos.Service;

public class ServiceEditDto
{
    public static Expression<Func<ServiceModel, ServiceEditDto>> Mapper(string lang)
    {
        var departmentExpression = DepartmentSimpleDto.Mapper(lang);
        var partnerLinkExpression = ServicePartnerCreateDto.Mapper(lang);
        var categoryExpression = ServiceCategorySimpleDto.Mapper(lang);
        var serviceProviderChannelExpression = ServiceProviderChannelSimpleDto.Mapper(lang);
        var serviceDeliveryChannelExpression = ServiceDeliveryChannelSimpleDto.Mapper(lang);
        var serviceClientCategoryExpression = ServiceClientCategorySimpleDto.Mapper(lang);

        return item => new ServiceEditDto
        {
            Id = item.Id,
            Ownership = item.Ownership,
            MainServiceNameAr = item.MainServiceNameAr,
            MainServiceNameEn = item.MainServiceNameEn,
            SubServiceNameAr = item.SubServiceNameAr,
            SubServiceNameEn = item.SubServiceNameEn,
            SupplementaryServiceNameAr = item.SupplementaryServiceNameAr,
            SupplementaryServiceNameEn = item.SupplementaryServiceNameEn,
            Categories = item.CategoryLinks.Select(x => categoryExpression.Invoke(x.Category)).ToList(),
            DescriptionAr = item.DescriptionAr,
            DescriptionEn = item.DescriptionEn,
            Limitation = item.Limitation,
            IsThereAPackage = item.IsThereAPackage == 1,
            Package = item.Package,
            Types = item.Types,
            DeliveryTime = item.DeliveryTime,
            PaymentMethods = item.PaymentMethods,
            Duration = item.Duration,
            ElectronicTransformationRatio = item.ElectronicTransformationRatio,
            IsElectronicallyConvertible = item.IsElectronicallyConvertible == 1,
            IndividualServiceInsurance = item.IndividualServiceInsurance,
            BusinessServiceInsurance = item.BusinessServiceInsurance,
            GovernmentServiceInsurance = item.GovernmentServiceInsurance,
            Procedures = item.Procedures,
            QueryStage = item.QueryStage,
            ApplicationStage = item.ApplicationStage,
            DeliveryStage = item.DeliveryStage,
            ReceiptOfTheRequest = item.ReceiptOfTheRequest,
            IsLinkedWithOtherParties = item.IsLinkedWithOtherParties == 1,
            LinkedPartyName = item.LinkedPartyName,
            LinkedPartyService = item.LinkedPartyService,
            Development = item.Development,
            DevelopmentEntrances = item.DevelopmentEntrances,
            OwningDepartment = item.OwningDepartmentId == null
                ? null
                : departmentExpression.Invoke(item.OwningDepartment),
            Departments = item.DepartmentLinks.Select(x => departmentExpression.Invoke(x.Department)).ToList(),
            Partners = item.PartnerLinks.Select(x => partnerLinkExpression.Invoke(x)).ToList(),
            ProviderChannels = item.ServiceProviderChannelLinks
                .Select(x => serviceProviderChannelExpression.Invoke(x.ServiceProviderChannel)).ToList(),
            DeliveryChannels = item.ServiceDeliveryChannelLinks
                .Select(x => serviceDeliveryChannelExpression.Invoke(x.ServiceDeliveryChannel)).ToList(),
            ClientCategories = item.ServiceClientCategoryLinks
                .Select(x => serviceClientCategoryExpression.Invoke(x.ServiceClientCategory)).ToList(),
            Proactive = item.Proactive,
            ProactiveStandards = item.ProactiveStandards,
            DevelopmentEffect = item.DevelopmentEffect,
            ImpactOnQualityOfLife = item.ImpactOnQualityOfLife,
            IsActive = item.IsActive == 1,
            CancelingReason = item.CancelingReason,
            DurationType = item.DurationType,
            PostEvaluationScore = item.PostEvaluationScore,
            EvaluationNotes = item.EvaluationNotes,
            Fees = item.Fees
        };
    }

    public Guid Id { get; set; }
    public string Ownership { get; set; }
    public string MainServiceNameAr { get; set; }
    public string MainServiceNameEn { get; set; }
    public string SubServiceNameAr { get; set; }
    public string SubServiceNameEn { get; set; }
    public string SupplementaryServiceNameAr { get; set; }
    public string SupplementaryServiceNameEn { get; set; }
    public IEnumerable<ServiceCategorySimpleDto> Categories { get; set; }
    public string DescriptionAr { get; set; }
    public string DescriptionEn { get; set; }
    public string Limitation { get; set; }
    public bool IsThereAPackage { get; set; }
    public string Package { get; set; }
    public IEnumerable<string> Types { get; set; }
    public string DeliveryTime { get; set; }
    public IEnumerable<string> PaymentMethods { get; set; }
    public string Duration { get; set; }
    public bool IsElectronicallyConvertible { get; set; }
    public string ElectronicTransformationRatio { get; set; }
    public string IndividualServiceInsurance { get; set; }
    public string BusinessServiceInsurance { get; set; }
    public string GovernmentServiceInsurance { get; set; }
    public string Procedures { get; set; }
    public string QueryStage { get; set; }
    public string ApplicationStage { get; set; }
    public string DeliveryStage { get; set; }
    public string ReceiptOfTheRequest { get; set; }
    public bool IsLinkedWithOtherParties { get; set; }
    public string LinkedPartyName { get; set; }
    public string LinkedPartyService { get; set; }
    public string Development { get; set; }
    public IEnumerable<string> DevelopmentEntrances { get; set; }
    public string Proactive { get; set; }
    public IEnumerable<string> ProactiveStandards { get; set; }
    public string DevelopmentEffect { get; set; }
    public string ImpactOnQualityOfLife { get; set; }
    public bool IsActive { get; set; }
    public string CancelingReason { get; set; }
    public string DurationType { get; set; }
    public decimal? PostEvaluationScore { get; set; }
    public string EvaluationNotes { get; set; }
    public DepartmentSimpleDto OwningDepartment { get; set; }
    public IEnumerable<DepartmentSimpleDto> Departments { get; set; }
    public ICollection<ServicePartnerCreateDto> Partners { get; set; }
    public ICollection<ServiceProviderChannelSimpleDto> ProviderChannels { get; set; }
    public ICollection<ServiceDeliveryChannelSimpleDto> DeliveryChannels { get; set; }
    public ICollection<ServiceClientCategorySimpleDto> ClientCategories { get; set; }
    public IEnumerable<ServiceFee> Fees { get; set; }
}
