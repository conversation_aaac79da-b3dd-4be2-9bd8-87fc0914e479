using System;
using System.Linq.Expressions;
using Injaz.Core.Dtos.CapacityPlanning.TimeDimension;
using Injaz.Core.Dtos.Department;
using LinqKit;

namespace Injaz.Core.Dtos.CapacityPlanning.ServiceChannelDemands;

public class ServiceChannelDemandEditDto
{
    public Guid Id { get; set; }
    public DepartmentSimpleDto Department { get; set; }
    public TimeDimensionSimpleDto Time { get; set; }

    public int RegularServiceVolume { get; set; }
    public int FastServiceVolume { get; set; }
    public int HappinessCenterVolume { get; set; }

    public int MoiAppVolume { get; set; }

    public int AjpAppVolume { get; set; }
    public int WebsiteVolume { get; set; }

    public static Expression<Func<Models.DomainClasses.App.CapacityPlanningModel.ServiceChannelDemand, ServiceChannelDemandEditDto>> Mapper(string lang)
    {
        var departmentExp = DepartmentSimpleDto.Mapper(lang);
        var timeDimensionExp = TimeDimensionSimpleDto.Mapper();
        return e => new ServiceChannelDemandEditDto
        {
            Id = e.Id,
            Department = e.Department != null ? departmentExp.Invoke(e.Department) : null,
            Time = e.Time != null ?  timeDimensionExp.Invoke(e.Time) : null ,
            RegularServiceVolume = e.RegularServiceVolume,
            FastServiceVolume = e.FastServiceVolume,
            HappinessCenterVolume = e.HappinessCenterVolume,
            MoiAppVolume = e.MoiAppVolume,
            AjpAppVolume = e.AjpAppVolume,
            WebsiteVolume = e.WebsiteVolume
        };
    }
}
