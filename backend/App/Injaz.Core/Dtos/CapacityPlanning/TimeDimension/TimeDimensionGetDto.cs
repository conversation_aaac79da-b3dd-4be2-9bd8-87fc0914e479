using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using Injaz.Core.Dtos.CapacityPlanning.DataEntryPeriods;
using LinqKit;

namespace Injaz.Core.Dtos.CapacityPlanning.TimeDimension;

public class TimeDimensionGetDto
{
    public Guid Id { get; set; }
    public int Year { get; set; }
    public int MonthNumber { get; set; }

    public IEnumerable<DataEntryPeriodGetDto> PeriodSettings { get; set; }

    public static Expression<Func<Models.DomainClasses.App.CapacityPlanningModel.TimeDimension, TimeDimensionGetDto>> Mapper()
    {
        var periodsExp = DataEntryPeriodGetDto.Mapper(HelperFunctions.GetLanguageCode());
        return e => new TimeDimensionGetDto
        {
            Id = e.Id,
            Year = e.Year,
            MonthNumber = e.MonthNumber,
            PeriodSettings = e.DataEntryPeriods
                .Select(x => periodsExp.Invoke(x))
                .ToList()
        };
    }
}
