using System;
using System.Linq;
using System.Linq.Expressions;
using Injaz.Core.Dtos.Department;
using LinqKit;

namespace Injaz.Core.Dtos.CapacityPlanning.CenterServiceLinks;

public class CenterServiceLinksListDto
{
    public Guid Id { get; set; }

    public DepartmentSimpleDto Department { get; set; }

    public int TotalServices { get; set; }


    public static Expression<Func<Models.DomainClasses.App.Department, CenterServiceLinksListDto>> Mapper()
    {
        var departmentExp = DepartmentSimpleDto.Mapper(HelperFunctions.GetLanguageCode());
        return e => new CenterServiceLinksListDto
        {
            Id = e.Id,
            Department =  departmentExp.Invoke(e),
            TotalServices = e.CenterServiceLinks.Any()
                ? e.CenterServiceLinks.Count : 0
        };
    }
}
