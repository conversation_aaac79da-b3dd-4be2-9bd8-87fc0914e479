using System;
using System.Linq.Expressions;

namespace Injaz.Core.Dtos.LinkedApplication;

public class LinkedApplicationSimpleDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.LinkedApplication, LinkedApplicationSimpleDto>>
        Mapper() => item => new LinkedApplicationSimpleDto
    {
        Id = item.Id,
        Name = item.Name
    };

    public Guid Id { get; set; }
    public string Name { get; set; }
}
