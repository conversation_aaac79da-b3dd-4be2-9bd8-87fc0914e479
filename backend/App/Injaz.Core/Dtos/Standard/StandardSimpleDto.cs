using System;
using System.Linq.Expressions;
using Injaz.Core.Constants;

namespace Injaz.Core.Dtos.Standard;

public class StandardSimpleDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.Standard, StandardSimpleDto>>
        Mapper(string lang) => item => new StandardSimpleDto
    {
        Id = item.Id,
        Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
    };

    public Guid Id { get; set; }
    public string Name { get; set; }
}
