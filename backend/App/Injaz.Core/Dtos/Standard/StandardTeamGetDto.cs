using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.Pillar;
using Injaz.Core.Dtos.StandardTask;

namespace Injaz.Core.Dtos.Standard;

public class StandardTeamGetDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.Standard, StandardTeamGetDto>>
        Mapper(string lang)
    {
        var pillarExpression = PillarHierarchyDto.Mapper(lang);
        var memberExpression = StandardMemberWithSummaryDto.Mapper(lang);
        var progressExpression = HelperExpression.StandardProgress();

        return item => new StandardTeamGetDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
            Weight = item.Weight,
            Progress = progressExpression.Invoke(item),
            Pillar = pillarExpression.Invoke(item.Pillar),
            Members = item.UserLinks.Select(x => memberExpression.Invoke(x)).ToList(),
            SubtasksStatistics = StandardSubtaskStatisticsDto.Mapper().Invoke(item),
        };
    }

    public Guid Id { get; set; }
    public string Name { get; set; }
    public double Weight { get; set; }
    public double? Progress { get; set; }
    public PillarHierarchyDto Pillar { get; set; }
    public IEnumerable<StandardMemberWithSummaryDto> Members { get; set; }
    public StandardSubtaskStatisticsDto SubtasksStatistics { get; set; }
}
