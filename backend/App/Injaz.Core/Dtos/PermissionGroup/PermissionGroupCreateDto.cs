using System.Collections.Generic;
using System.ComponentModel.DataAnnotations;

namespace Injaz.Core.Dtos.PermissionGroup;

public class PermissionGroupCreateDto
{
    [MaxLength(1024)]
    [Display(Name = "name_in_arabic")]
    [Required(ErrorMessage = "0_is_required")]
    public string NameAr { get; set; }

    [MaxLength(1024)]
    [Display(Name = "description_in_arabic")]
    public string DescriptionAr { get; set; }

    [MaxLength(1024)]
    [Display(Name = "name_in_english")]
    public string NameEn { get; set; }

    [MaxLength(1024)]
    [Display(Name = "description_in_arabic")]
    public string DescriptionEn { get; set; }

    [Display(Name = "permission_list")] public IEnumerable<string> PermissionIdList { get; set; }
}
