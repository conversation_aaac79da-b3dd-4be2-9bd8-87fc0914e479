using System;
using System.Linq.Expressions;
using Injaz.Core.Constants;

namespace Injaz.Core.Dtos.PermissionGroup;

public class PermissionGroupSimpleDto
{
    public static Expression<Func<Core.Models.DomainClasses.Permission.PermissionGroup, PermissionGroupSimpleDto>> Mapper(
        string lang)
    {
        return item => new PermissionGroupSimpleDto
        {
            Id = item.Id,
            Name = lang.Equals(SupportedCultures.LanguageEnglish) ? item.NameEn : item.NameAr,
        };
    }

    public Guid Id { get; set; }
    public string Name { get; set; }
}
