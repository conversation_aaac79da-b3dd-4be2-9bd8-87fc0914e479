using System;
using System.Collections.Generic;
using System.Linq;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.Activity;
using Injaz.Core.Dtos.Award;
using Injaz.Core.Dtos.Innovation;
using Injaz.Core.Dtos.TrainingProgram;
using InnovatorModel = Injaz.Core.Models.DomainClasses.App.Innovator;


namespace Injaz.Core.Dtos.Innovator;

public class InnovatorGetDto
{
    public static Expression<Func<InnovatorModel, InnovatorGetDto>> Mapper(string lang)
    {
        var activityExpression = ActivityListDto.Mapper(lang);
        var awardExpression = AwardGetDto.Mapper(lang);
        var trainingProgramExpression = TrainingProgramGetDto.Mapper(lang);
        var innovationExpression = InnovationDetailDto.Mapper(lang);

        return item => new InnovatorGetDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageEnglish ? item.NameEn : item.NameAr,
            EmployeeNumber = item.EmployeeNumber,
            Rank = item.Rank,
            HasALogo = item.HasALogo == 1,
            AwardCount = item.Awards.Count(),
            InnovationCount = item.Innovations.Count(),
            TrainingProgramCount = item.TrainingPrograms.Count(),
            TrainingHours = item.TrainingPrograms.Select(x => x.Hours).Sum(),
            Awards = item.Awards.Select(x => awardExpression.Invoke(x)).ToList(),
            TrainingPrograms = item.TrainingPrograms.Select(x => trainingProgramExpression.Invoke(x)).ToList(),
            Innovations = item.Innovations.Select(x => innovationExpression.Invoke(x)).ToList()
        };
    }
    public Guid Id { get; set; }
    public string Name { get; set; }
    public string EmployeeNumber { get; set; }
    public string Rank { get; set; }
    public bool HasALogo { get; set; }
    public int AwardCount { get; set; }
    public int TrainingHours { get; set; }
    public int TrainingProgramCount { get; set; }
    public int InnovationCount { get; set; }
    public string ImageUrl { get; set; }
    public IEnumerable<TrainingProgramGetDto> TrainingPrograms { get; set; }
    public IEnumerable<AwardGetDto> Awards { get; set; }
    public IEnumerable<InnovationDetailDto> Innovations { get; set; }
}
