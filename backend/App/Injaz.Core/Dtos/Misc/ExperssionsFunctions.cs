using System;
using System.Linq.Expressions;

namespace Injaz.Core.Dtos.Misc;

public static class ExpressionsFunctions
{
    // TODO: Bad approach, should not do translation inside an expression!
    public static Expression<Func<int?, string>> GetLevelName(string lang)
    {
        var levelString = lang == "en" ? "Level " : "المستوى ";
        var notDefined = lang == "en" ? "Not defined" : "غير محددد";
        return level => level == null ? notDefined :
            level == 1 ? $"{level} 1" :
            level == 2 ? $"{level} 2" :
            level == 3 ? $"{level} 3" :
            level == 4 ? $"{level} 4" : null;
    }
}
