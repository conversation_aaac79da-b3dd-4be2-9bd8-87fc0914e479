using System;
using System.ComponentModel.DataAnnotations;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Dtos.KpiResult;
using Injaz.Core.Models.DomainClasses.App;

namespace Injaz.Core.Dtos.Benchmark;

public class BenchmarkKpiResultCreateDto
{
    public static Expression<Func<BenchmarkKpiResultLink, BenchmarkKpiResultCreateDto>> Mapper(
        string lang,
        bool canAchievedBeNegative
    )
    {
        var expression = KpiResultSummaryDto.Mapper(lang, canAchievedBeNegative);
        return item => new BenchmarkKpiResultCreateDto
        {
            Result = expression.Invoke(item.Result),
            PartnerResult = item.PartnerResult
        };
    }

    [Display(Name = "result")]
    [Required(ErrorMessage = "0_is_required")]
    public KpiResultSummaryDto Result { get; set; }

    [Display(Name = "partner_result")]
    [Required(ErrorMessage = "0_is_required")]
    public double PartnerResult { get; set; }
}