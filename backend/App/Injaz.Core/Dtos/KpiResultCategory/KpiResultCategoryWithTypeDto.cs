using System;
using System.Linq.Expressions;
using Injaz.Core.Constants;

namespace Injaz.Core.Dtos.KpiResultCategory;

public class KpiResultCategoryWithTypeDto
{
    public static Expression<Func<Core.Models.DomainClasses.App.KpiResultCategory, KpiResultCategoryWithTypeDto>>
        Mapper(string lang) => item => new KpiResultCategoryWithTypeDto
    {
        Id = item.Id,
        Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
        Type = item.Type,
    };

    public Guid Id { get; set; }
    public string Name { get; set; }
    public string Type { get; set; }
}
