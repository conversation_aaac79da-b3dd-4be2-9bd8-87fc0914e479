using System.ComponentModel.DataAnnotations;
using UserRequestModel = Injaz.Core.Models.DomainClasses.App.UserRequest;

namespace Injaz.Core.Dtos.UserRequest;

public class UserRequestCreateDto
{

    [Display(Name = "request_content")]
    [Required(ErrorMessage = "0_is_required")]
    public string Content { get; set; }

    [Display(Name = "request_type")]
    [Required(ErrorMessage = "0_is_required")]
    [RegularExpression("^(?:" +
                       UserRequestModel.RequestTypeFile + "|" +
                       UserRequestModel.RequestTypeOperation + "|" +
                       UserRequestModel.RequestTypeKpi +
                       ")$",
        ErrorMessage = "0_is_invalid")]
    public string Type { get; set; }
}
