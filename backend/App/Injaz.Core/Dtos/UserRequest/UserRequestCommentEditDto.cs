using System;
using System.ComponentModel.DataAnnotations;
using System.Linq.Expressions;
using LinqKit;
using Injaz.Core.Dtos.User;
using Injaz.Core.Models.DomainClasses.App;

namespace Injaz.Core.Dtos.UserRequest;

public class UserRequestCommentEditDto : UserRequestCommentCreateDto
{
    public static Expression<Func<UserRequestComment, UserRequestCommentEditDto>> Mapper(string lang)
    {
        var userExpression = UserSimpleDto.Mapper(lang);

        return item => new UserRequestCommentEditDto
        {
            Id = item.Id,
            Content = item.Content,
            User = userExpression.Invoke(item.User)
        };
    }
    [Display(Name = "id")]
    [Required(ErrorMessage = "0_is_required")]
    public Guid Id { get; set; }
    public UserSimpleDto User { get; set; }

}
