using System;
using System.Linq.Expressions;
using Injaz.Core.Constants;
using Injaz.Core.Models.DomainClasses.App.RiskModel;

namespace Injaz.Core.Dtos.RiskDtos.RiskImpacts;

public class RiskImpactSimpleDto
{
    public static Expression<Func<RiskImpact, RiskImpactSimpleDto>> Mapper(string lang)
    {
        return item => new RiskImpactSimpleDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
        };
    }

    public Guid Id { get; set; }

    public string Name { get; set; }
}
