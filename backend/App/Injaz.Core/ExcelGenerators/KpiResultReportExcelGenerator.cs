using System;
using System.Collections.Generic;
using System.Drawing;
using System.Linq;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.KpiResult;
using Injaz.Core.Dtos.Report;
using Injaz.Core.ExcelGenerators.Misc;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Resources.Shared;
using Microsoft.Extensions.Localization;
using OfficeOpenXml.Style;

namespace Injaz.Core.ExcelGenerators;

public class KpiResultReportExcelGenerator : IDisposable
{
    private readonly IList<ReportKpiResultDto> _data;

    private readonly ReportKpiResultSettingDto _settings;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly int[] _years;
    private readonly AppSetting _appSetting;

    private BaseExcelFile _file;

    private int _resultStartColumn;
    private int _totalColumns;
    private int _totalRows;
    private int _rowsPerHeader;
    private int _rowsPerRecord;
    private int _resultColumnCountPerYear;


    public KpiResultReportExcelGenerator(
        IList<ReportKpiResultDto> data,
        ReportKpiResultSettingDto settings,
        IStringLocalizer<SharedResource> localizer,
        int[] years,
        AppSetting appSetting
    )

    {
        _data = data;
        _settings = settings;
        _localizer = localizer;
        _years = years;
        _appSetting = appSetting;
        _file = new BaseExcelFile();

        PreProcess();
    }

    public void Dispose()
    {
        _file.Dispose();
    }

    public byte[] Generate()
    {
        if (HelperFunctions.GetLanguageCode() == SupportedCultures.LanguageArabic)
        {
            _file.SetRtl();
        }

        _file
            .Select(0, 0, _totalRows, _totalColumns)
            .SetBorder(ExcelBorderStyle.Thin)
            .SetVerticalAlignment(ExcelVerticalAlignment.Center)
            .SetRowHeight(30)
            .SetFontSize(16)
            .SetFontName("Sakkal Majalla")
            .Deselect();

        PopulateKpiResultList(_data);


        // For some reason I cannot set the wrap
        // property when filling the data.
        _file
            .Select(0, 1, _totalRows, 2)
            .SetTextWrap(true)
            .Deselect();

        return _file.GetBytes();
    }

    private void PopulateKpiResultList(IEnumerable<ReportKpiResultDto> kpiResults)
    {
        PopulateGroupHeader();

        // Different style for headers.
        _file.Select(
                _file.Position.Row,
                0,
                _file.Position.Row + _rowsPerHeader,
                _totalColumns
            )
            .SetBold(true)
            .SetPatternType(ExcelFillStyle.Solid)
            .SetBackgroundColor(Color.Khaki)
            .Deselect()
            .MoveDown(_rowsPerHeader)
            .Move(_file.Position.Row, 0);

        kpiResults.ToList()
            .ForEach(kpi =>
            {
                PopulateResult(kpi);

                _file
                    .MoveDown(_rowsPerRecord)
                    .Move(_file.Position.Row, 0);
            });
    }

    private void PopulateGroupHeader()
    {
        BaseExcelFile PrepCells()
        {
            return _file.Select(
                    _file.Position.Row,
                    _file.Position.Col,
                    _file.Position.Row + _rowsPerHeader,
                    _file.Position.Col + 1
                )
                .Merge()
                .Deselect();
        }

        void AddYear(int year)
        {
            // Merge the year cells.
            _file.Select(
                    _file.Position.Row,
                    _file.Position.Col,
                    _file.Position.Row + 1,
                    _file.Position.Col + _resultColumnCountPerYear
                )
                .Merge()
                .Deselect()
                .SetValue(year)
                .MoveDown();

            _file.SetValue("A").SetColumnWidth(20).MoveRight();
            _file.SetValue("B").SetColumnWidth(20).MoveRight();
            _file.SetValue(_localizer["target"]).SetColumnWidth(20).MoveRight();
            _file.SetValue(_localizer["result"]).SetColumnWidth(20).MoveRight();
            _file.SetValue(_localizer["achieved"]).SetColumnWidth(20).MoveRight()
                .MoveUp();
        }

        PrepCells().SetValue(_localizer["code"]).SetColumnWidth(20).MoveRight();
        PrepCells().SetValue(_localizer["name"]).SetColumnWidth(45).SetTextWrap(true).MoveRight();
        PrepCells().SetValue(_localizer["department"]).SetColumnWidth(35).SetTextWrap(true).MoveRight();

        if (_settings.Columns.Units) PrepCells().SetValue(_localizer["units"]).SetColumnWidth(15).MoveRight();
        if (_settings.Columns.Formula) PrepCells().SetValue(_localizer["formula"]).SetColumnWidth(20).MoveRight();
        if (_settings.Columns.MeasurementCycle)
            PrepCells().SetValue(_localizer["cycle"]).SetColumnWidth(15).MoveRight();
        if (_settings.Columns.MeasuringDepartment)
            PrepCells().SetValue(_localizer["measuring_department"]).SetColumnWidth(15).MoveRight();

        if (_settings.Columns.FormulaDescriptionA)
            PrepCells()
                .SetValue(_localizer["formula_description_a"])
                .SetColumnWidth(35)
                .MoveRight();

        if (_settings.Columns.FormulaDescriptionB)
            PrepCells()
                .SetValue(_localizer["formula_description_b"])
                .SetColumnWidth(35)
                .MoveRight();

        if (_settings.Columns.Type) PrepCells().SetValue(_localizer["type"]).SetColumnWidth(15).MoveRight();
        if (_settings.Columns.Operation)
            PrepCells().SetValue(_localizer["linked_operations"]).SetColumnWidth(60).MoveRight();


        // Add result header.
        _years.ToList().ForEach(year => AddYear(year));

        // Center all header cells.
        _file
            .Select(_file.Position.Row, 0, _file.Position.Row + _rowsPerHeader, _totalColumns)
            .SetHorizontalAlignment(ExcelHorizontalAlignment.Center)
            .Deselect();
    }

    private void PopulateResult(ReportKpiResultDto reportRecord)
    {
        var colorCodings = _appSetting.KpiSetting.AchievementColorCodings.ToArray();


        BaseExcelFile PrepCells()
        {
            return _file.Select(
                    _file.Position.Row,
                    _file.Position.Col,
                    _file.Position.Row + _rowsPerRecord,
                    _file.Position.Col + 1
                )
                .Merge()
                .Deselect();
        }

        void FillPeriodCells(
            double? a,
            double? b,
            double? result,
            double? target,
            double? achieved
        )
        {
            var color = HelperFunctions
                .ExtractColorFromString(
                    AppHelperFunctions.GetAchievedColor(achieved, colorCodings)
                );

            _file
                .Select(
                    _file.Position.Row,
                    _file.Position.Col,
                    _file.Position.Row + 1,
                    _file.Position.Col + _resultColumnCountPerYear
                )
                .SetHorizontalAlignment(ExcelHorizontalAlignment.Center)
                .Deselect()
                .SetValue(a).SetNumberFormat("#0.00").MoveRight()
                .SetValue(b).SetNumberFormat("#0.00").MoveRight()
                .SetValue(target).SetNumberFormat("#0.00").MoveRight()
                .SetValue(result).SetNumberFormat("#0.00").MoveRight()
                .SetValue(achieved)
                .SetNumberFormat("#0.00%")
                .SetPatternType(ExcelFillStyle.Solid)
                .SetBackgroundColor(Color.FromArgb(color.Red, color.Green, color.Blue)) // cspell:ignore Argb
                .MoveRight();
        }

        void FillResultCells(KpiResultSummaryDto result)
        {
            if (result == null)
            {
                _file.MoveRight(_resultColumnCountPerYear);
                return;
            }

            FillPeriodCells(
                result.AggregateA,
                result.AggregateB,
                result.Result,
                result.Target,
                result.Achieved
            );
        }

        PrepCells()
            .SetValue($"{_appSetting.AppId}-{reportRecord.Kpi.Type.Code}-{reportRecord.Kpi.Code}")
            .SetHorizontalAlignment(ExcelHorizontalAlignment.Center)
            .MoveRight();

        PrepCells().SetValue(reportRecord.Kpi.Name).MoveRight();
        PrepCells().SetValue(reportRecord.Department.Name).MoveRight();

        if (_settings.Columns.Units) PrepCells().SetValue(_localizer[reportRecord.Kpi.Units]).MoveRight();
        if (_settings.Columns.Formula) PrepCells().SetValue(reportRecord.Kpi.Formula).MoveRight();
        if (_settings.Columns.MeasurementCycle)
            PrepCells().SetValue(_localizer[reportRecord.Kpi.MeasurementCycle]).MoveRight();
        if (_settings.Columns.MeasuringDepartment)
            PrepCells().SetValue(reportRecord.Kpi.MeasuringDepartment?.Name).MoveRight();
        if (_settings.Columns.FormulaDescriptionA)
            PrepCells().SetValue(reportRecord.Kpi.FormulaDescriptionA).MoveRight();
        if (_settings.Columns.FormulaDescriptionB)
            PrepCells().SetValue(reportRecord.Kpi.FormulaDescriptionB).MoveRight();
        if (_settings.Columns.Type) PrepCells().SetValue(reportRecord.Kpi.Type.Name).MoveRight();
        if (_settings.Columns.Operation)
            PrepCells()
                .SetValue(string.Join(" - ", reportRecord.Kpi.Operations.Select(x => x.Name)))
                .MoveRight();

        _years
            .Select(x => reportRecord.Results.FirstOrDefault(y => y.Year.Equals(x)))
            .ToList()
            .ForEach(x => FillResultCells(x));
    }

    private void PreProcess()
    {
        // kpi code, kpi name, department
        const int fixedColumnCount = 3;

        // a, b, result, target, achieved.
        _resultColumnCountPerYear = 5;

        // Processing columns
        // Figuring out the total number of columns.
        var values = _settings
            .Columns
            .GetType()
            .GetProperties()
            .ToList()
            .Select(x => x.GetValue(_settings.Columns))
            .ToArray();
        if (values.Any(x => x == null || x.GetType() != typeof(bool)))
        {
            throw new Exception("One (or more) of the columns values is not bool.");
        }


        _resultStartColumn = values.Select(x => (bool)x).Count(x => x) + fixedColumnCount + 1;


        _totalColumns = _resultStartColumn - 1 + _resultColumnCountPerYear * _years.Length;

        // Processing rows
        _rowsPerHeader = 2;
        _rowsPerRecord = 1;

        _totalRows =
            // Header
            _rowsPerHeader +
            _data.Count() * _rowsPerRecord;
    }
}
