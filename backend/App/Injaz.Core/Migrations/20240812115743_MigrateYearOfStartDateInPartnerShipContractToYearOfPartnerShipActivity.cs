using Microsoft.EntityFrameworkCore.Migrations;

namespace Injaz.Core.Migrations
{
    public partial class MigrateYearOfStartDateInPartnerShipContractToYearOfPartnerShipActivity : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Use a transaction to ensure atomicity
            migrationBuilder.Sql(
                @"
                UPDATE pa
                SET pa.year = DATEPART(YEAR, pc.start_date)
                FROM partnership_activities pa
                INNER JOIN partnership_contracts pc
                    ON pa.partnership_contract_id = pc.Id;
                ");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
        }
    }
}
