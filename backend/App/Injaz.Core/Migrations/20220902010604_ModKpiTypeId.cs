using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Injaz.Core.Migrations
{
    public partial class ModKpiTypeId : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "type_id",
                table: "kpis",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_kpis_type_id",
                table: "kpis",
                column: "type_id");

            migrationBuilder.AddForeignKey(
                name: "FK_kpis_kpi_types_type_id",
                table: "kpis",
                column: "type_id",
                principalTable: "kpi_types",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_kpis_kpi_types_type_id",
                table: "kpis");

            migrationBuilder.DropIndex(
                name: "IX_kpis_type_id",
                table: "kpis");

            migrationBuilder.DropColumn(
                name: "type_id",
                table: "kpis");
        }
    }
}
