using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Injaz.Core.Migrations
{
    public partial class AddApprvlFlwTx : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "approval_flow_transactions",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    user_id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    entity_id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    value = table.Column<string>(type: "nvarchar(128)", maxLength: 128, nullable: true),
                    label = table.Column<string>(type: "nvarchar(512)", maxLength: 512, nullable: true),
                    note = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    creation_time = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_approval_flow_transactions", x => x.id);
                    table.ForeignKey(
                        name: "FK_approval_flow_transactions_users_user_id",
                        column: x => x.user_id,
                        principalTable: "users",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_approval_flow_transactions_entity_id",
                table: "approval_flow_transactions",
                column: "entity_id");

            migrationBuilder.CreateIndex(
                name: "IX_approval_flow_transactions_label",
                table: "approval_flow_transactions",
                column: "label");

            migrationBuilder.CreateIndex(
                name: "IX_approval_flow_transactions_user_id",
                table: "approval_flow_transactions",
                column: "user_id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "approval_flow_transactions");
        }
    }
}
