// <auto-generated />
using System;
using Injaz.Core.Models;
using Microsoft.EntityFrameworkCore;
using Microsoft.EntityFrameworkCore.Infrastructure;
using Microsoft.EntityFrameworkCore.Metadata;
using Microsoft.EntityFrameworkCore.Migrations;
using Microsoft.EntityFrameworkCore.Storage.ValueConversion;

namespace Injaz.Core.Migrations
{
    [DbContext(typeof(DatabaseInitializer))]
    [Migration("20240529120251_MigrateDataFromServiceToServiceDeliveryChannelLink")]
    partial class MigrateDataFromServiceToServiceDeliveryChannelLink
    {
        protected override void BuildTargetModel(ModelBuilder modelBuilder)
        {
#pragma warning disable 612, 618
            modelBuilder
                .HasAnnotation("Relational:MaxIdentifierLength", 128)
                .HasAnnotation("ProductVersion", "6.0.0-preview.7.21378.4")
                .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

            modelBuilder.Entity("MNMWebApp.OAuth.Models.RefreshToken", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<int>("IsValid")
                        .HasColumnType("int")
                        .HasColumnName("is_valid");

                    b.Property<string>("Subject")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("subject");

                    b.HasKey("Id");

                    b.ToTable("refresh_tokens");
                });

            modelBuilder.Entity("Injaz.Core.Evaluate.Models.EvaluationScoreDetail", b =>
                {
                    b.Property<double?>("Score")
                        .HasColumnType("float")
                        .HasColumnName("score");

                    b.Property<string>("Color")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("color");

                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("NameAr")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("name_en");

                    b.HasKey("Score");

                    b.ToSqlQuery("workaround for table create/drop");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Activity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<int>("Year")
                        .HasColumnType("int")
                        .HasColumnName("year");

                    b.HasKey("Id");

                    b.ToTable("activities");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.AppSetting", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("AppId")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("app_id");

                    b.Property<string>("AppNameAr")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("app_name_ar");

                    b.Property<string>("AppNameEn")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("app_name_en");

                    b.Property<string>("AppPrimaryColor")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("app_primary_color");

                    b.Property<string>("AppSecondaryColor")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("app_secondary_color");

                    b.Property<string>("AppUrl")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("app_url");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<string>("DashboardTopItems")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("dashboard_top_items");

                    b.Property<string>("KpiSetting")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("kpi_setting");

                    b.Property<string>("LdapSetting")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("ldap_setting");

                    b.Property<string>("MailSetting")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("mail_setting");

                    b.Property<string>("Office365Setting")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("office_365_setting");

                    b.Property<string>("PlanSetting")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("plan_setting");

                    b.HasKey("Id");

                    b.ToTable("app_settings");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Award", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<Guid>("InnovatorId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("innovator_id");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<int>("Year")
                        .HasColumnType("int")
                        .HasColumnName("year");

                    b.HasKey("Id");

                    b.HasIndex("InnovatorId");

                    b.ToTable("awards");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Benchmark", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("Agenda")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("agenda");

                    b.Property<string>("CoordinatorEmail")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("coordinator_email");

                    b.Property<string>("CoordinatorEmployeeNumber")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("coordinator_employee_number");

                    b.Property<string>("CoordinatorFullName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("coordinator_full_name");

                    b.Property<string>("CoordinatorOfficeNumber")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("coordinator_office_number");

                    b.Property<string>("CoordinatorPhone")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("coordinator_phone");

                    b.Property<string>("CoordinatorRank")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("coordinator_rank");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<Guid>("DepartmentId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("department_id");

                    b.Property<string>("EntityName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("entity_name");

                    b.Property<string>("EntityType")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("entity_type");

                    b.Property<DateTime?>("FirstApprovalTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("first_approval_time");

                    b.Property<Guid?>("FirstApprovalUserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("first_approval_user_id");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Language")
                        .HasMaxLength(2)
                        .HasColumnType("nvarchar(2)")
                        .HasColumnName("language");

                    b.Property<string>("ManagementType")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("management_type");

                    b.Property<string>("Method")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("method");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("OtherRequestReasons")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("other_request_reasons");

                    b.Property<string>("OtherSelectionReasons")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("other_selection_reasons");

                    b.Property<Guid?>("PartnerId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("partner_id");

                    b.Property<string>("ReportDetails")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("report_details");

                    b.Property<DateTime?>("ReportTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("report_time");

                    b.Property<string>("ReportType")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("report_type");

                    b.Property<DateTime?>("SecondApprovalTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("second_approval_time");

                    b.Property<Guid?>("SecondApprovalUserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("second_approval_user_id");

                    b.Property<string>("Type")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("type");

                    b.Property<DateTime>("VisitDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("visit_date");

                    b.HasKey("Id");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("FirstApprovalUserId");

                    b.HasIndex("PartnerId");

                    b.HasIndex("SecondApprovalUserId");

                    b.ToTable("benchmarks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.BenchmarkBenchmarkRequestReasonLink", b =>
                {
                    b.Property<Guid>("BenchmarkId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("benchmark_id");

                    b.Property<Guid>("ReasonId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("reason_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("BenchmarkId", "ReasonId");

                    b.HasIndex("ReasonId");

                    b.ToTable("benchmark_benchmark_request_reason_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.BenchmarkBenchmarkSelectionReasonLink", b =>
                {
                    b.Property<Guid>("BenchmarkId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("benchmark_id");

                    b.Property<Guid>("ReasonId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("reason_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("BenchmarkId", "ReasonId");

                    b.HasIndex("ReasonId");

                    b.ToTable("benchmark_benchmark_selection_reason_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.BenchmarkKpiResultLink", b =>
                {
                    b.Property<Guid>("BenchmarkId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("benchmark_id");

                    b.Property<Guid>("ResultId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("result_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<double>("PartnerResult")
                        .HasColumnType("float")
                        .HasColumnName("partner_result");

                    b.HasKey("BenchmarkId", "ResultId");

                    b.HasIndex("ResultId");

                    b.ToTable("benchmark_kpi_result_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.BenchmarkLibraryFileLink", b =>
                {
                    b.Property<Guid>("BenchmarkId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("benchmark_id");

                    b.Property<Guid>("LibraryFileId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("library_file_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description");

                    b.HasKey("BenchmarkId", "LibraryFileId");

                    b.HasIndex("LibraryFileId");

                    b.ToTable("benchmark_library_file_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.BenchmarkOperationLink", b =>
                {
                    b.Property<Guid>("BenchmarkId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("benchmark_id");

                    b.Property<Guid>("OperationId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("operation_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("BenchmarkId", "OperationId");

                    b.HasIndex("OperationId");

                    b.ToTable("benchmark_strategic_operation_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.BenchmarkOtherManagement", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid>("BenchmarkId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("benchmark_id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<string>("Detail")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("detail");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Type")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("type");

                    b.HasKey("Id");

                    b.HasIndex("BenchmarkId");

                    b.ToTable("benchmark_other_management");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.BenchmarkRequestReason", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.HasKey("Id");

                    b.ToTable("benchmark_request_reasons");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.BenchmarkSelectionReason", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.HasKey("Id");

                    b.ToTable("benchmark_selection_reason");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.BenchmarkStrategicGoalLink", b =>
                {
                    b.Property<Guid>("BenchmarkId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("benchmark_id");

                    b.Property<Guid>("GoalId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("goal_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("BenchmarkId", "GoalId");

                    b.HasIndex("GoalId");

                    b.ToTable("benchmark_strategic_goal_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.BenchmarkVisitor", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid>("BenchmarkId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("benchmark_id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<string>("Department")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("department");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("email");

                    b.Property<string>("EmployeeNumber")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("employee_number");

                    b.Property<string>("EmploymentTitle")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("employment_title");

                    b.Property<string>("FullName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("full_name");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Phone")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("phone");

                    b.Property<string>("Rank")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("rank");

                    b.HasKey("Id");

                    b.HasIndex("BenchmarkId");

                    b.ToTable("benchmark_visitor");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Capability", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<string>("DescriptionAr")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description_ar");

                    b.Property<string>("DescriptionEn")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description_en");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<Guid>("TypeId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("type_id");

                    b.Property<int>("Year")
                        .HasColumnType("int")
                        .HasColumnName("year");

                    b.HasKey("Id");

                    b.HasIndex("TypeId");

                    b.ToTable("capabilities");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.CapabilityKpiLink", b =>
                {
                    b.Property<Guid>("CapabilityId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("capability_id");

                    b.Property<Guid>("KpiId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("kpi_id");

                    b.Property<int>("Order")
                        .HasColumnType("int")
                        .HasColumnName("order");

                    b.HasKey("CapabilityId", "KpiId");

                    b.HasIndex("KpiId");

                    b.ToTable("capability_kpi_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.CapabilityLibraryFileLink", b =>
                {
                    b.Property<Guid>("CapabilityId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("capability_id");

                    b.Property<Guid>("LibraryFileId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("library_file_id");

                    b.HasKey("CapabilityId", "LibraryFileId");

                    b.HasIndex("LibraryFileId");

                    b.ToTable("capability_library_file_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.CapabilityType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.HasKey("Id");

                    b.ToTable("capability_types");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Department", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<string>("HierarchyCode")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("hierarchy_code");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<int>("IsMain")
                        .HasColumnType("int")
                        .HasColumnName("is_main");

                    b.Property<int?>("LevelNumber")
                        .HasColumnType("int")
                        .HasColumnName("level_number");

                    b.Property<string>("ManagerEmail")
                        .HasMaxLength(8192)
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("manager_email");

                    b.Property<string>("ManagerName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("manager_name");

                    b.Property<string>("ManagerStaffNumber")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("manager_staff_number");

                    b.Property<string>("MangerTitle")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("manager_title");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<string>("OldOrgId")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("org_id");

                    b.Property<int>("Order")
                        .HasColumnType("int")
                        .HasColumnName("order");

                    b.Property<Guid?>("OrganizationTypeId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("organization_type_id");

                    b.Property<Guid?>("ParentDepartmentId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("parent_department_id");

                    b.HasKey("Id");

                    b.HasIndex("HierarchyCode")
                        .IsUnique()
                        .HasFilter("[hierarchy_code] IS NOT NULL");

                    b.HasIndex("OrganizationTypeId");

                    b.HasIndex("ParentDepartmentId");

                    b.ToTable("departments");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.DepartmentKpiLink", b =>
                {
                    b.Property<Guid>("DepartmentId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("department_id");

                    b.Property<Guid>("KpiId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("kpi_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("DepartmentId", "KpiId");

                    b.HasIndex("KpiId");

                    b.ToTable("department_kpi_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.DepartmentUserLink", b =>
                {
                    b.Property<Guid>("DepartmentId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("department_id");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("user_id");

                    b.Property<int>("IsAuthorizedForSigning")
                        .HasColumnType("int")
                        .HasColumnName("is_authorized_for_signing");

                    b.HasKey("DepartmentId", "UserId");

                    b.HasIndex("UserId");

                    b.ToTable("department_user_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Email", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("Body")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("body");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<DateTime?>("LockTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("lock_time");

                    b.Property<DateTime?>("SentTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("sent_time");

                    b.Property<string>("Subject")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("subject");

                    b.Property<string>("To")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("to");

                    b.HasKey("Id");

                    b.ToTable("emails");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.EvaluationModel.Evaluation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<string>("DescriptionAr")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description_ar");

                    b.Property<string>("DescriptionEn")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description_en");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("type");

                    b.HasKey("Id");

                    b.HasIndex("Type");

                    b.ToTable("evaluations");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.EvaluationModel.EvaluationInstance", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<string>("DescriptionAr")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description_ar");

                    b.Property<string>("DescriptionEn")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description_en");

                    b.Property<Guid>("EntityId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("entity_id");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("note");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("type");

                    b.HasKey("Id");

                    b.HasIndex("EntityId");

                    b.HasIndex("Type");

                    b.ToTable("evaluation_instances");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.EvaluationModel.EvaluationScoreBand", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("Color")
                        .HasMaxLength(16)
                        .HasColumnType("nvarchar(16)")
                        .HasColumnName("color");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid>("EvaluationId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("evaluation_id");

                    b.Property<double?>("From")
                        .HasColumnType("float")
                        .HasColumnName("from");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<double?>("To")
                        .HasColumnType("float")
                        .HasColumnName("to");

                    b.HasKey("Id");

                    b.HasIndex("EvaluationId");

                    b.ToTable("evaluation_score_bands");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.EvaluationModel.EvaluationStandard", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid>("EvaluationId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("evaluation_id");

                    b.Property<string>("NameAr")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("name_en");

                    b.Property<double>("Target")
                        .HasColumnType("float")
                        .HasColumnName("target");

                    b.HasKey("Id");

                    b.HasIndex("EvaluationId");

                    b.ToTable("evaluation_standards");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.EvaluationModel.EvaluationStandardRecord", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<Guid>("InstanceId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("instance_id");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("note");

                    b.Property<double>("Target")
                        .HasColumnType("float")
                        .HasColumnName("target");

                    b.Property<double?>("Value")
                        .HasColumnType("float")
                        .HasColumnName("value");

                    b.HasKey("Id");

                    b.HasIndex("InstanceId");

                    b.ToTable("evaluation_standard_records");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.FlowTransaction", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DepartmentId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("department_id");

                    b.Property<Guid>("EntityId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("entity_id");

                    b.Property<string>("Label")
                        .HasMaxLength(512)
                        .HasColumnType("nvarchar(512)")
                        .HasColumnName("label");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("note");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("user_id");

                    b.Property<string>("Value")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasColumnName("value");

                    b.HasKey("Id");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("EntityId");

                    b.HasIndex("Label");

                    b.HasIndex("UserId");

                    b.ToTable("flow_transactions");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.GovernmentStrategicGoal", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.HasKey("Id");

                    b.ToTable("government_strategic_goals");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.ImprovementOpportunity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("ActionDescription")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("action_description");

                    b.Property<string>("ApplicationEffect")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("application_effect");

                    b.Property<string>("Assigned")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("assigned");

                    b.Property<Guid?>("AssignedDepartmentId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("assigned_department_id");

                    b.Property<Guid?>("AssignedTeamId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("assigned_team_id");

                    b.Property<Guid?>("AssignedUserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("assigned_user_id");

                    b.Property<string>("ClosingProjectName")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("closing_project_name");

                    b.Property<DateTime?>("ClosingStartDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("closing_start_date");

                    b.Property<string>("CompletionRate")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("completion_rate");

                    b.Property<decimal?>("Cost")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("cost");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<Guid?>("InputCategoryId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("input_category_id");

                    b.Property<string>("InputsCategory")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("inputs_category");

                    b.Property<DateTime?>("InputsDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("inputs_date");

                    b.Property<string>("InputsDescription")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("inputs_description");

                    b.Property<string>("InputsSources")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("inputs_sources");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<Guid?>("OwningDepartmentId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("owning_department_id");

                    b.Property<DateTime?>("PlannedClosingDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("planned_closing_date");

                    b.Property<string>("Principles")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("principles");

                    b.Property<string>("Priority")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("priority");

                    b.Property<string>("Result")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("result");

                    b.Property<string>("Risks")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("risks");

                    b.HasKey("Id");

                    b.HasIndex("AssignedDepartmentId");

                    b.HasIndex("AssignedTeamId");

                    b.HasIndex("AssignedUserId");

                    b.HasIndex("InputCategoryId");

                    b.HasIndex("OwningDepartmentId");

                    b.ToTable("improvement_opportunities");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.ImprovementOpportunityInputCategory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.HasKey("Id");

                    b.ToTable("improvement_opportunity_input_categories");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.ImprovementOpportunityInputSource", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.HasKey("Id");

                    b.ToTable("improvement_opportunity_input_sources");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.ImprovementOpportunityInputSourceLink", b =>
                {
                    b.Property<Guid>("OpportunityId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("opportunity_id");

                    b.Property<Guid>("SourceId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("source_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("OpportunityId", "SourceId");

                    b.HasIndex("SourceId");

                    b.ToTable("improvement_opportunity_input_source_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.ImprovementOpportunityLibraryFileLink", b =>
                {
                    b.Property<Guid>("ImprovementOpportunityId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("improvement_opportunity_id");

                    b.Property<Guid>("LibraryFileId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("library_file_id");

                    b.HasKey("ImprovementOpportunityId", "LibraryFileId");

                    b.HasIndex("LibraryFileId");

                    b.ToTable("improvement_opportunity_library_file_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.ImprovementOpportunityOperationLink", b =>
                {
                    b.Property<Guid>("ImprovementOpportunityId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("improvement_opportunity_id");

                    b.Property<Guid>("OperationId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("operation_id");

                    b.HasKey("ImprovementOpportunityId", "OperationId");

                    b.HasIndex("OperationId");

                    b.ToTable("improvement_opportunity_operation_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.ImprovementOpportunityPrincipleLink", b =>
                {
                    b.Property<Guid>("ImprovementOpportunityId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("improvement_opportunity_id");

                    b.Property<Guid>("PrincipleId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("principle_id");

                    b.HasKey("ImprovementOpportunityId", "PrincipleId");

                    b.HasIndex("PrincipleId");

                    b.ToTable("improvement_opportunity_principle_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.ImprovementOpportunityStandardLink", b =>
                {
                    b.Property<Guid>("ImprovementOpportunityId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("improvement_opportunity_id");

                    b.Property<Guid>("StandardId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("standard_id");

                    b.HasKey("ImprovementOpportunityId", "StandardId");

                    b.HasIndex("StandardId");

                    b.ToTable("improvement_opportunity_standard_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Innovation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("ContentType")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("content_type");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<string>("Details")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("details");

                    b.Property<string>("DocumentationType")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("documentation_type");

                    b.Property<string>("FileName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("file_name");

                    b.Property<string>("Impact")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("impact");

                    b.Property<Guid>("InnovatorId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("innovator_id");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("SuggestionNumber")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("suggestion_number");

                    b.Property<string>("Title")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("title");

                    b.Property<int>("Year")
                        .HasColumnType("int")
                        .HasColumnName("year");

                    b.HasKey("Id");

                    b.HasIndex("InnovatorId");

                    b.ToTable("innovations");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.InnovationActivityLink", b =>
                {
                    b.Property<Guid>("InnovationId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("innovation_id");

                    b.Property<Guid>("ActivityId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("activity_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("InnovationId", "ActivityId");

                    b.HasIndex("ActivityId");

                    b.ToTable("innovation_activity_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Innovator", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<string>("EmployeeNumber")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("employee_number");

                    b.Property<int>("HasALogo")
                        .HasColumnType("int")
                        .HasColumnName("has_a_logo");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<string>("Rank")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("rank");

                    b.HasKey("Id");

                    b.ToTable("innovators");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Kpi", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("AggregationTypeA")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("aggregation_type_a");

                    b.Property<string>("AggregationTypeB")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("aggregation_type_b");

                    b.Property<Guid?>("BalancedBehaviorCardId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("balanced_behavior_card_id");

                    b.Property<string>("Code")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("code");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<int>("CreationYear")
                        .HasColumnType("int")
                        .HasColumnName("year");

                    b.Property<string>("DataEntryMethod")
                        .IsRequired()
                        .HasMaxLength(16)
                        .HasColumnType("nvarchar(16)")
                        .HasColumnName("data_entry_method");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<string>("DescriptionAr")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description_ar");

                    b.Property<string>("DescriptionEn")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description_en");

                    b.Property<string>("Direction")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("direction");

                    b.Property<string>("Entity")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("entity");

                    b.Property<string>("Formula")
                        .HasMaxLength(265)
                        .HasColumnType("nvarchar(265)")
                        .HasColumnName("formula");

                    b.Property<string>("FormulaDescriptionAAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("formula_description_a_ar");

                    b.Property<string>("FormulaDescriptionAEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("formula_description_a_en");

                    b.Property<string>("FormulaDescriptionBAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("formula_description_b_ar");

                    b.Property<string>("FormulaDescriptionBEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("formula_description_b_en");

                    b.Property<double?>("InitialResult")
                        .HasColumnType("float")
                        .HasColumnName("initial_result");

                    b.Property<string>("InitialResultDetails")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("initial_result_details");

                    b.Property<string>("InitialResultSource")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)")
                        .HasColumnName("initial_result_source");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<int>("IsSpecial")
                        .HasColumnType("int")
                        .HasColumnName("is_special");

                    b.Property<int>("IsTrend")
                        .HasColumnType("int")
                        .HasColumnName("is_trend");

                    b.Property<string>("MeasurementCycle")
                        .HasMaxLength(16)
                        .HasColumnType("nvarchar(16)")
                        .HasColumnName("measurement_cycle");

                    b.Property<string>("MeasurementMethod")
                        .HasMaxLength(16)
                        .HasColumnType("nvarchar(16)")
                        .HasColumnName("measurement_method");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<Guid>("OwningDepartmentId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("owning_department_id");

                    b.Property<string>("Source")
                        .HasMaxLength(30)
                        .HasColumnType("nvarchar(30)")
                        .HasColumnName("source");

                    b.Property<string>("Status")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("status");

                    b.Property<Guid>("TypeId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("type_id");

                    b.Property<string>("Units")
                        .HasMaxLength(16)
                        .HasColumnType("nvarchar(16)")
                        .HasColumnName("units");

                    b.Property<string>("UnitsDescription")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("units_description");

                    b.HasKey("Id");

                    b.HasIndex("BalancedBehaviorCardId");

                    b.HasIndex("OwningDepartmentId");

                    b.HasIndex("TypeId");

                    b.ToTable("kpis");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiBalancedBehaviorCard", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.HasKey("Id");

                    b.ToTable("kpi_balanced_behavior_card");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiBenchmark", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("CompetitiveEffect")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("competitive_effect");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<string>("EntityName")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("entity_name");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<Guid>("KpiId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("kpi_id");

                    b.Property<Guid?>("LibraryFileId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("library_file_id");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<double?>("Result")
                        .HasColumnType("float")
                        .HasColumnName("result");

                    b.Property<int>("Year")
                        .HasColumnType("int")
                        .HasColumnName("year");

                    b.HasKey("Id");

                    b.HasIndex("KpiId");

                    b.HasIndex("LibraryFileId");

                    b.ToTable("kpi_benchmark");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiDynamicDataEntryRequest", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<double?>("A")
                        .HasColumnType("float")
                        .HasColumnName("a");

                    b.Property<double?>("B")
                        .HasColumnType("float")
                        .HasColumnName("b");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<string>("FlowState")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("flow_state");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<Guid>("PeriodId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("period_id");

                    b.HasKey("Id");

                    b.HasIndex("PeriodId");

                    b.ToTable("kpi_dynamic_data_entry_requests");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiDynamicDataEntryRequestLibraryFileLink", b =>
                {
                    b.Property<Guid>("RequestId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("request_id");

                    b.Property<Guid>("LibraryFileId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("library_file_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("RequestId", "LibraryFileId");

                    b.HasIndex("LibraryFileId");

                    b.ToTable("kpi_dynamic_data_entry_request_library_file_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiKpiTagLink", b =>
                {
                    b.Property<Guid>("KpiId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("kpi_id");

                    b.Property<Guid>("TagId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("tag_id");

                    b.HasKey("KpiId", "TagId");

                    b.HasIndex("TagId");

                    b.ToTable("kpi_kpi_tag_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResult", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("AggregationTypeA")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("aggregation_type_a");

                    b.Property<string>("AggregationTypeB")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("aggregation_type_b");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<Guid>("DepartmentId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("department_id");

                    b.Property<string>("Formula")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("formula");

                    b.Property<string>("FormulaDescriptionAAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("formula_description_a_ar");

                    b.Property<string>("FormulaDescriptionAEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("formula_description_a_en");

                    b.Property<string>("FormulaDescriptionBAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("formula_description_b_ar");

                    b.Property<string>("FormulaDescriptionBEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("formula_description_b_en");

                    b.Property<string>("InputModeA")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("input_mode_a");

                    b.Property<string>("InputModeB")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("input_mode_b");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<int>("IsOwning")
                        .HasColumnType("int")
                        .HasColumnName("is_owning");

                    b.Property<Guid>("KpiId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("kpi_id");

                    b.Property<DateTime>("LatestModificationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("latest_modification_time");

                    b.Property<string>("MeasurementCycle")
                        .HasMaxLength(16)
                        .HasColumnType("nvarchar(16)")
                        .HasColumnName("measurement_cycle");

                    b.Property<string>("MeasurementMethod")
                        .HasMaxLength(16)
                        .HasColumnType("nvarchar(16)")
                        .HasColumnName("measurement_method");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<Guid>("ModifiedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("modified_by_id");

                    b.Property<double?>("Target")
                        .HasColumnType("float")
                        .HasColumnName("target");

                    b.Property<string>("TargetSettingMethod")
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)")
                        .HasColumnName("target_setting_method");

                    b.Property<string>("TargetSettingMethodEntity")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("target_setting_method_entity");

                    b.Property<string>("TargetSettingMethodTarget")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("target_setting_method_target");

                    b.Property<double?>("TargetZero")
                        .HasColumnType("float")
                        .HasColumnName("target_zero");

                    b.Property<string>("Units")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("units");

                    b.Property<string>("UnitsDescription")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("units_description");

                    b.Property<int>("Year")
                        .HasColumnType("int")
                        .HasColumnName("year");

                    b.HasKey("Id");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("KpiId");

                    b.HasIndex("ModifiedById");

                    b.ToTable("kpi_results");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultAttachment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("ContentType")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("content_type");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<string>("FileName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("file_name");

                    b.Property<Guid?>("KpiResultDataEntryResponseId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("kpi_result_data_entry_response_id");

                    b.Property<Guid?>("KpiResultId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("kpi_result_id");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<int>("Period")
                        .HasColumnType("int")
                        .HasColumnName("period");

                    b.Property<string>("PeriodType")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("period_type");

                    b.HasKey("Id");

                    b.HasIndex("KpiResultDataEntryResponseId");

                    b.HasIndex("KpiResultId");

                    b.ToTable("kpi_result_attachment");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultBreakdown", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<int>("IsMandatory")
                        .HasColumnType("int")
                        .HasColumnName("is_mandatory");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("Parameter")
                        .HasMaxLength(1)
                        .HasColumnType("nvarchar(1)")
                        .HasColumnName("parameter");

                    b.Property<Guid>("ResultId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("result_id");

                    b.Property<int>("ShouldMatchOriginalValue")
                        .HasColumnType("int")
                        .HasColumnName("should_match_original_value");

                    b.Property<Guid>("SubcategoryId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("subcategory_id");

                    b.HasKey("Id");

                    b.HasIndex("ResultId");

                    b.HasIndex("SubcategoryId");

                    b.ToTable("kpi_result_breakdowns");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultCapability", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<string>("DescriptionAr")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description_ar");

                    b.Property<string>("DescriptionEn")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description_en");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<Guid?>("KpiResultDataEntryResponsePeriodId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("kpi_result_data_entry_response_period_id");

                    b.Property<Guid?>("KpiResultPeriodId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("kpi_result_period_id");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<Guid>("TypeId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("type_id");

                    b.HasKey("Id");

                    b.HasIndex("KpiResultDataEntryResponsePeriodId");

                    b.HasIndex("KpiResultPeriodId");

                    b.HasIndex("TypeId");

                    b.ToTable("kpi_result_capabilities");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultCapabilityLibraryFileLink", b =>
                {
                    b.Property<Guid>("CapabilityId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("capability_id");

                    b.Property<Guid>("LibraryFileId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("library_file_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("CapabilityId", "LibraryFileId");

                    b.HasIndex("LibraryFileId");

                    b.ToTable("kpi_result_capability_library_file_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultCapabilityType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.HasKey("Id");

                    b.ToTable("kpi_result_capability_types");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultCategory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("type");

                    b.HasKey("Id");

                    b.ToTable("kpi_result_categories");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultDataEntryRequest", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<DateTime>("EndTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("end_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<int>("IsSpecial")
                        .HasColumnType("int")
                        .HasColumnName("is_special");

                    b.Property<string>("MeasurementCycle")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("measurement_cycle");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("note");

                    b.Property<string>("Path")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("path");

                    b.Property<string>("Periods")
                        .IsRequired()
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("periods");

                    b.Property<DateTime>("StartTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("start_time");

                    b.Property<int>("Year")
                        .HasColumnType("int")
                        .HasColumnName("year");

                    b.HasKey("Id");

                    b.ToTable("kpi_result_data_entry_requests");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultDataEntryRequestDepartmentLink", b =>
                {
                    b.Property<Guid>("RequestId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("request_id");

                    b.Property<Guid>("DepartmentId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("department_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("RequestId", "DepartmentId");

                    b.HasIndex("DepartmentId");

                    b.ToTable("kpi_result_data_entry_request_department_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultDataEntryRequestKpiLink", b =>
                {
                    b.Property<Guid>("RequestId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("request_id");

                    b.Property<Guid>("KpiId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("kpi_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("RequestId", "KpiId");

                    b.HasIndex("KpiId");

                    b.ToTable("kpi_result_data_entry_request_kpi_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultDataEntryRequestNotification", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid>("RequestId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("response_id");

                    b.Property<string>("Type")
                        .HasMaxLength(64)
                        .HasColumnType("nvarchar(64)")
                        .HasColumnName("type");

                    b.HasKey("Id");

                    b.HasIndex("RequestId");

                    b.ToTable("kpi_result_data_entry_request_notifications");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultDataEntryResponse", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<Guid>("RequestId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("request_id");

                    b.Property<Guid>("ResultId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("result_id");

                    b.HasKey("Id");

                    b.HasIndex("RequestId");

                    b.HasIndex("ResultId");

                    b.ToTable("kpi_result_data_entry_responses");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultDataEntryResponsePeriod", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<double?>("A")
                        .HasColumnType("float")
                        .HasColumnName("a");

                    b.Property<double?>("B")
                        .HasColumnType("float")
                        .HasColumnName("b");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<string>("ImprovementProcedure")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("improvement_procedure");

                    b.Property<string>("ImprovementProcedureCompletionPercentage")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("improvement_procedure_completion_percentage");

                    b.Property<DateTime?>("ImprovementProcedureExpectedCompletionDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("improvement_procedure_expected_completion_date");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<int>("Period")
                        .HasColumnType("int")
                        .HasColumnName("period");

                    b.Property<Guid>("ResponseId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("response_id");

                    b.Property<string>("ResultAnalysis")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("result_analysis");

                    b.HasKey("Id");

                    b.HasIndex("ResponseId");

                    b.ToTable("kpi_result_data_entry_response_periods");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultDataEntryResponseTransfer", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("Assignee")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("assignee");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<string>("Direction")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("direction");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("notes");

                    b.Property<Guid>("ResponseId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("response_id");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("ResponseId");

                    b.ToTable("kpi_result_data_entry_response_transfers");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultDataEntryResponseTransferUserLink", b =>
                {
                    b.Property<Guid>("TransferId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("transfer_id");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("user_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("TransferId", "UserId");

                    b.HasIndex("UserId");

                    b.ToTable("kpi_result_data_entry_response_transfer_user_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultPeriod", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<double?>("A")
                        .HasColumnType("float")
                        .HasColumnName("a");

                    b.Property<double?>("B")
                        .HasColumnType("float")
                        .HasColumnName("b");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<string>("ImprovementProcedure")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("improvement_procedure");

                    b.Property<string>("ImprovementProcedureCompletionPercentage")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("improvement_procedure_completion_percentage");

                    b.Property<DateTime?>("ImprovementProcedureExpectedCompletionDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("improvement_procedure_expected_completion_date");

                    b.Property<int>("IsApproved")
                        .HasColumnType("int")
                        .HasColumnName("is_approved");

                    b.Property<Guid>("KpiResultId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("kpi_result_id");

                    b.Property<string>("LeadershipDirective")
                        .HasMaxLength(8192)
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("leadership_directive");

                    b.Property<int>("Period")
                        .HasColumnType("int")
                        .HasColumnName("period");

                    b.Property<string>("ResultAnalysis")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("result_analysis");

                    b.Property<string>("SupervisorNote")
                        .HasMaxLength(8192)
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("supervisor_note");

                    b.Property<double?>("Target")
                        .HasColumnType("float")
                        .HasColumnName("target");

                    b.HasKey("Id");

                    b.HasIndex("KpiResultId", "Period")
                        .IsUnique();

                    b.ToTable("kpi_result_periods");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultPeriodBreakdown", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid>("BreakdownId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("breakdown_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid>("PeriodId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("period_id");

                    b.Property<double?>("Value")
                        .HasColumnType("float")
                        .HasColumnName("value");

                    b.HasKey("Id");

                    b.HasIndex("BreakdownId");

                    b.HasIndex("PeriodId");

                    b.ToTable("kpi_result_period_breakdowns");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultRequest", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("Content")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("content");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsClosed")
                        .HasColumnType("int")
                        .HasColumnName("is_closed");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<Guid>("KpiResultId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("kpi_result_id");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("Title")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("title");

                    b.Property<string>("Type")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("type");

                    b.HasKey("Id");

                    b.HasIndex("KpiResultId");

                    b.ToTable("kpi_result_requests");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultRequestComment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("Content")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("content");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<Guid>("RequestId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("request_id");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("user_id");

                    b.HasKey("Id");

                    b.HasIndex("RequestId");

                    b.HasIndex("UserId");

                    b.ToTable("kpi_result_request_comments");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultSubcategory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid>("CategoryId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("category_id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.ToTable("kpi_result_subcategories");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiStrategicGoalLink", b =>
                {
                    b.Property<Guid>("KpiId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("kpi_id");

                    b.Property<Guid>("StrategicGoalId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("strategic_goal_id");

                    b.HasKey("KpiId", "StrategicGoalId");

                    b.HasIndex("StrategicGoalId");

                    b.ToTable("kpi_strategic_goal_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiTag", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.HasKey("Id");

                    b.ToTable("kpi_tags");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("code");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsConsideredInDashboard")
                        .HasColumnType("int")
                        .HasColumnName("is_considered_in_dashboard");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<int>("IsExcludedFromCalculations")
                        .HasColumnType("int")
                        .HasColumnName("is_excluded_from_calculations");

                    b.Property<int>("IsLinkableToPlans")
                        .HasColumnType("int")
                        .HasColumnName("is_linkable_to_plans");

                    b.Property<int>("IsUsedToComputeGoalAchievement")
                        .HasColumnType("int")
                        .HasColumnName("is_used_to_compute_goal_achievement");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<int>("Order")
                        .HasColumnType("int")
                        .HasColumnName("order");

                    b.HasKey("Id");

                    b.ToTable("kpi_types");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.LibraryFile", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("ContentType")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("content_type");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<string>("FileName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("file_name");

                    b.Property<int?>("FileSize")
                        .HasColumnType("int")
                        .HasColumnName("file_size");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<DateTime>("LatestModificationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("latest_modification_time");

                    b.Property<string>("Link")
                        .HasMaxLength(8192)
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("link");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<Guid>("OwnerId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("owner_id");

                    b.HasKey("Id");

                    b.HasIndex("OwnerId");

                    b.ToTable("library_files");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.LibraryFileLibraryTagLink", b =>
                {
                    b.Property<Guid>("LibraryFileId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("library_file_id");

                    b.Property<Guid>("TagId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("tag_id");

                    b.HasKey("LibraryFileId", "TagId");

                    b.HasIndex("TagId");

                    b.ToTable("library_file_library_tag_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.LibraryTag", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.HasKey("Id");

                    b.ToTable("library_tags");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.LinkedApplication", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<string>("HashedApiKey")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("hashed_api_key");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("name");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("user_id");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("linked_applications");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.MinistryStrategicGoal", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.HasKey("Id");

                    b.ToTable("ministry_strategic_goals");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.NationalAgenda", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.HasKey("Id");

                    b.ToTable("national_agendas");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Notification", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<string>("DescriptionAr")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description_ar");

                    b.Property<string>("DescriptionEn")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description_en");

                    b.Property<Guid?>("TargetId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("target_id");

                    b.Property<string>("TargetMetadata")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("target_metadata");

                    b.Property<string>("TargetType")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("target_type");

                    b.Property<string>("TitleAr")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("title_ar");

                    b.Property<string>("TitleEn")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("title_en");

                    b.HasKey("Id");

                    b.ToTable("notifications");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.NotificationUserLink", b =>
                {
                    b.Property<Guid>("NotificationId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("notification_id");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("user_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<DateTime?>("ReadTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("read_time");

                    b.HasKey("NotificationId", "UserId");

                    b.HasIndex("UserId");

                    b.ToTable("notification_user_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Operation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("Beneficiaries")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("beneficiaries");

                    b.Property<string>("Code")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("code");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<string>("DangerNumber")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("danger_number");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<string>("DescriptionAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("description_ar");

                    b.Property<string>("DescriptionEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("description_en");

                    b.Property<string>("InputType")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("input_type");

                    b.Property<string>("Inputs")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("operation_inputs");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<int>("Level")
                        .HasColumnType("int")
                        .HasColumnName("operation_level");

                    b.Property<Guid?>("MainFLowChartId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("main_flow_chart_id");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<int>("Number")
                        .HasColumnType("int")
                        .HasColumnName("number");

                    b.Property<string>("Output")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("output");

                    b.Property<string>("OutputType")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("output_type");

                    b.Property<string>("Outputs")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("outputs");

                    b.Property<Guid?>("OwnerDepartmentId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("owner_department_id");

                    b.Property<Guid?>("ParentId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("parent_id");

                    b.Property<string>("Purpose")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("purpose");

                    b.Property<string>("SupplierCategory")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("supplier_category");

                    b.Property<string>("SupplierName")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("supplier_name");

                    b.Property<string>("SustainabilityImpact")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("sustainability_impacts");

                    b.Property<string>("TechnicalSolutions")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("technical_solutions");

                    b.Property<string>("Terminologies")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("terminologies");

                    b.Property<string>("Types")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("types");

                    b.Property<double?>("Weight")
                        .HasColumnType("float")
                        .HasColumnName("weight");

                    b.HasKey("Id");

                    b.HasIndex("MainFLowChartId");

                    b.HasIndex("OwnerDepartmentId");

                    b.HasIndex("ParentId");

                    b.ToTable("operations");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OperationEnhancement", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("BusinessModelFileId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("business_model_file_id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<DateTime>("Date")
                        .HasColumnType("datetime2")
                        .HasColumnName("date");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<string>("Description")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("description");

                    b.Property<Guid?>("FlowChartFileId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("flow_chart_file_id");

                    b.Property<int>("FlowChartFileNumber")
                        .HasColumnType("int")
                        .HasColumnName("flow_chart_file_number");

                    b.Property<string>("Impact")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("impact");

                    b.Property<string>("Input")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("input");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<int>("Number")
                        .HasColumnType("int")
                        .HasColumnName("number");

                    b.Property<Guid>("OperationId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("operation_id");

                    b.Property<string>("OutputType")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("output_type");

                    b.Property<string>("Outputs")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("outputs");

                    b.Property<string>("Tools")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("tools");

                    b.Property<Guid?>("TypeId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("type_id");

                    b.HasKey("Id");

                    b.HasIndex("BusinessModelFileId");

                    b.HasIndex("FlowChartFileId");

                    b.HasIndex("OperationId");

                    b.HasIndex("TypeId");

                    b.ToTable("operation_enhancements");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OperationEnhancementFileLink", b =>
                {
                    b.Property<Guid>("FileId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("file_id");

                    b.Property<Guid>("EnhancementId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("enhancement_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("FileId", "EnhancementId");

                    b.HasIndex("EnhancementId");

                    b.ToTable("operation_enhancement_file_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OperationEnhancementType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.HasKey("Id");

                    b.ToTable("operation_enhancement_types");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OperationExecutor", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Name")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name");

                    b.Property<Guid>("OperationId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("operation_id");

                    b.Property<Guid?>("PartnerId")
                        .HasColumnType("uniqueidentifier");

                    b.Property<string>("Type")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("type");

                    b.HasKey("Id");

                    b.HasIndex("OperationId");

                    b.HasIndex("PartnerId");

                    b.ToTable("operation_executor");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OperationFormFileLink", b =>
                {
                    b.Property<Guid>("OperationId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("operation_id");

                    b.Property<Guid>("FileId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("file_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<string>("Type")
                        .HasMaxLength(16)
                        .HasColumnType("nvarchar(16)")
                        .HasColumnName("type");

                    b.HasKey("OperationId", "FileId");

                    b.HasIndex("FileId");

                    b.ToTable("operation_form_file_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OperationKpiLink", b =>
                {
                    b.Property<Guid>("KpiId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("kpi_id");

                    b.Property<Guid>("OperationId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("operation_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("KpiId", "OperationId");

                    b.HasIndex("OperationId");

                    b.ToTable("operation_kpi_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OperationPartnerLink", b =>
                {
                    b.Property<Guid>("OperationId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("operation_id");

                    b.Property<Guid>("PartnerId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("partner_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("OperationId", "PartnerId");

                    b.HasIndex("PartnerId");

                    b.ToTable("operation_partner_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OperationPolicyLink", b =>
                {
                    b.Property<Guid>("PolicyId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("policy_id");

                    b.Property<Guid>("OperationId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("operation_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("PolicyId", "OperationId");

                    b.HasIndex("OperationId");

                    b.ToTable("operation_policy_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OperationProcedure", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("Code")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("code");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("Duration")
                        .HasColumnType("int")
                        .HasColumnName("duration");

                    b.Property<Guid?>("FlowchartFileId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("flowchart_file_id");

                    b.Property<string>("Goal")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("goal");

                    b.Property<string>("InCharge")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("in_charge");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<Guid>("OperationId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("operation_id");

                    b.Property<string>("Risk")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("risk");

                    b.Property<string>("Type")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("type");

                    b.Property<int>("Version")
                        .HasColumnType("int")
                        .HasColumnName("version");

                    b.Property<DateTime?>("VersionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("version_time");

                    b.HasKey("Id");

                    b.HasIndex("FlowchartFileId");

                    b.HasIndex("OperationId");

                    b.ToTable("operation_procedures");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OperationProcedureKpiLink", b =>
                {
                    b.Property<Guid>("ProcedureId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("procedure_id");

                    b.Property<Guid>("KpiId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("kpi_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("ProcedureId", "KpiId");

                    b.HasIndex("KpiId");

                    b.ToTable("operation_procedure_kpi_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OperationProcedureStep", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("notes");

                    b.Property<int?>("Order")
                        .HasColumnType("int")
                        .HasColumnName("order");

                    b.Property<Guid>("ProcedureId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("procedure_id");

                    b.Property<string>("Responsibility")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("responsibility");

                    b.Property<string>("Title")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("title");

                    b.Property<Guid?>("UsedModelFileId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("used_model_file_id");

                    b.HasKey("Id");

                    b.HasIndex("ProcedureId");

                    b.HasIndex("UsedModelFileId");

                    b.ToTable("operation_procedure_steps");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OperationRuleAndRegulation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.HasKey("Id");

                    b.ToTable("operation_rules_and_regulations");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OperationRuleAndRegulationLink", b =>
                {
                    b.Property<Guid>("RuleAndRegulationId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("rule_and_regulation_id");

                    b.Property<Guid>("OperationId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("operation_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("RuleAndRegulationId", "OperationId");

                    b.HasIndex("OperationId");

                    b.ToTable("operation_rule_and_regulation_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OperationSpecification", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.HasKey("Id");

                    b.ToTable("operation_specifications");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OperationSpecificationLink", b =>
                {
                    b.Property<Guid>("SpecificationId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("specification_id");

                    b.Property<Guid>("OperationId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("operation_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("SpecificationId", "OperationId");

                    b.HasIndex("OperationId");

                    b.ToTable("operation_specification_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OperationStrategicGoalLink", b =>
                {
                    b.Property<Guid>("OperationId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("operation_id");

                    b.Property<Guid>("GoalId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("goal_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("OperationId", "GoalId");

                    b.HasIndex("GoalId");

                    b.ToTable("operation_strategic_goal_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OperationSuccessFactorLink", b =>
                {
                    b.Property<Guid>("OperationId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("operation_id");

                    b.Property<Guid>("SuccessFactorId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("success_factor_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("OperationId", "SuccessFactorId");

                    b.HasIndex("SuccessFactorId");

                    b.ToTable("operations_success_factors_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OrganizationType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<int>("LevelNumber")
                        .HasColumnType("int")
                        .HasColumnName("level_number");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.HasKey("Id");

                    b.ToTable("organization_type");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnerModel.Partner", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.HasKey("Id");

                    b.ToTable("partners");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnerModel.PartnerEvaluationStandard", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<double>("Value")
                        .HasColumnType("float")
                        .HasColumnName("value");

                    b.HasKey("Id");

                    b.ToTable("partner_evaluation_standards");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnerModel.PartnerStandard", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<string>("SectorType")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("sector_type");

                    b.HasKey("Id");

                    b.ToTable("partner_standards");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipActivity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CommunicationToolId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("communication_tool_id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<Guid>("PartnershipContractId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("partnership_contract_id");

                    b.Property<string>("Purpose")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("purpose");

                    b.HasKey("Id");

                    b.HasIndex("CommunicationToolId");

                    b.HasIndex("PartnershipContractId");

                    b.ToTable("partnership_activities");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipActivityCommunicationTool", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.HasKey("Id");

                    b.ToTable("partnership_activity_communication_tools");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipActivityPeriod", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<bool>("IsLocked")
                        .HasColumnType("bit")
                        .HasColumnName("is_locked");

                    b.Property<Guid>("PartnershipActivityId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("partnership_activity_id");

                    b.Property<int>("Period")
                        .HasColumnType("int")
                        .HasColumnName("period");

                    b.Property<double?>("Target")
                        .HasColumnType("float")
                        .HasColumnName("target");

                    b.Property<double?>("Value")
                        .HasColumnType("float")
                        .HasColumnName("value");

                    b.HasKey("Id");

                    b.HasIndex("PartnershipActivityId");

                    b.ToTable("partnership_activity_periods");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipActivityPeriodAttachment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("ContentType")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("content_type");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<string>("FileName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("file_name");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<Guid?>("PartnershipActivityPeriodId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("partnership_activity_period_id");

                    b.HasKey("Id");

                    b.HasIndex("PartnershipActivityPeriodId");

                    b.ToTable("partnership_activity_period_attachments");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipContract", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("AgreementFileId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("agreement_file_id");

                    b.Property<string>("Arrangements")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("arrangements");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<string>("CurrentSituation")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("current_situation");

                    b.Property<string>("DealingWithConflicts")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("dealing_with_conflicts");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<Guid>("DepartmentId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("department_id");

                    b.Property<Guid?>("FieldId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("field_id");

                    b.Property<string>("FlowState")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("flow_state");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<bool>("IsRenewable")
                        .HasColumnType("bit")
                        .HasColumnName("is_renewable");

                    b.Property<string>("Membership")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("membership");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("OtherPartner")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("other_partner");

                    b.Property<string>("PartnerCapabilities")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("partner_capabilities");

                    b.Property<Guid?>("PartnerId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("partner_id");

                    b.Property<Guid?>("PartnershipTypeId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("partnership_type_id");

                    b.Property<string>("Purpose")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("purpose");

                    b.Property<string>("Resources")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("resources");

                    b.Property<string>("RolesAndResponsibilities")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("roles_and_responsibilities");

                    b.Property<string>("SectorType")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("sector_type");

                    b.Property<DateTime>("StartDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("start_date");

                    b.Property<string>("Terms")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("terms");

                    b.Property<string>("TimeFrame")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("time_frame");

                    b.Property<string>("TitleAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("title_ar");

                    b.Property<string>("TitleEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("title_en");

                    b.HasKey("Id");

                    b.HasIndex("AgreementFileId");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("FieldId");

                    b.HasIndex("PartnerId");

                    b.HasIndex("PartnershipTypeId");

                    b.ToTable("partnership_contracts");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipField", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.HasKey("Id");

                    b.ToTable("partnership_fields");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipFramework", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.HasKey("Id");

                    b.ToTable("partnership_frameworks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipFrameworkLink", b =>
                {
                    b.Property<Guid>("PartnershipContractId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("partnership_contract_id");

                    b.Property<Guid>("PartnershipFrameworkId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("partnership_framework_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("PartnershipContractId", "PartnershipFrameworkId");

                    b.HasIndex("PartnershipFrameworkId");

                    b.ToTable("partnership_framework_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipGoal", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<Guid>("PartnershipContractId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("partnership_contract_id");

                    b.Property<Guid>("StrategicGoalId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("strategic_goal_id");

                    b.HasKey("Id");

                    b.HasIndex("PartnershipContractId");

                    b.HasIndex("StrategicGoalId");

                    b.ToTable("partnership_goals");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipGoalActivity", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("Benefits")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("benefits");

                    b.Property<string>("ContentType")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("content_type");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<DateTime?>("EndDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("end_date");

                    b.Property<string>("FileName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("file_name");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<Guid>("PartnershipGoalId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("partnership_goal_id");

                    b.Property<string>("Responsibilities")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("responsibilities");

                    b.Property<string>("Risks")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("risks");

                    b.Property<string>("TargetKnowledge")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("target_knowledge");

                    b.Property<string>("Targets")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("targets");

                    b.HasKey("Id");

                    b.HasIndex("PartnershipGoalId");

                    b.ToTable("partnership_goal_activities");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipGoalActivityKpiLink", b =>
                {
                    b.Property<Guid>("PartnershipGoalActivityId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("partnership_goal_activity_id");

                    b.Property<Guid>("KpiId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("kpi_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("PartnershipGoalActivityId", "KpiId");

                    b.HasIndex("KpiId");

                    b.ToTable("partnership_goal_activity_kpi_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipGoalInitiative", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("Activities")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("activities");

                    b.Property<string>("Benefits")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("benefits");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<Guid>("PartnershipGoalId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("partnership_goal_id");

                    b.Property<string>("Responsibilities")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("responsibilities");

                    b.HasKey("Id");

                    b.HasIndex("PartnershipGoalId");

                    b.ToTable("partnership_goal_initiatives");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipGoalNationalAgendaLink", b =>
                {
                    b.Property<Guid>("PartnershipGoalId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("partnership_goal_id");

                    b.Property<Guid>("NationalAgendaId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("national_agenda_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("PartnershipGoalId", "NationalAgendaId");

                    b.HasIndex("NationalAgendaId");

                    b.ToTable("partnership_goal_national_agenda_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipGoalOperationLink", b =>
                {
                    b.Property<Guid>("PartnershipGoalId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("partnership_goal_id");

                    b.Property<Guid>("OperationId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("operation_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("PartnershipGoalId", "OperationId");

                    b.HasIndex("OperationId");

                    b.ToTable("partnership_goal_operation_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipGoalServiceLink", b =>
                {
                    b.Property<Guid>("PartnershipGoalId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("partnership_goal_id");

                    b.Property<Guid>("ServiceId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("service_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("PartnershipGoalId", "ServiceId");

                    b.HasIndex("ServiceId");

                    b.ToTable("partnership_goal_service_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipPartnerEvaluation", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("Label")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("label");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<Guid>("PartnershipContractId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("partnership_contract_id");

                    b.Property<double>("Target")
                        .HasColumnType("float")
                        .HasColumnName("target");

                    b.Property<double?>("Value")
                        .HasColumnType("float")
                        .HasColumnName("value");

                    b.HasKey("Id");

                    b.HasIndex("PartnershipContractId");

                    b.ToTable("partnership_partner_evaluations");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipPartnerStandardLink", b =>
                {
                    b.Property<Guid>("PartnershipContractId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("partnership_contract_id");

                    b.Property<Guid>("PartnerStandardId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("partner_standard_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("PartnershipContractId", "PartnerStandardId");

                    b.HasIndex("PartnerStandardId");

                    b.ToTable("partnership_partner_standard_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipScope", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<Guid?>("PartnershipFieldId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("partnership_field_id");

                    b.HasKey("Id");

                    b.HasIndex("PartnershipFieldId");

                    b.ToTable("partnership_scopes");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipScopeLink", b =>
                {
                    b.Property<Guid>("PartnershipContractId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("partnership_contract_id");

                    b.Property<Guid>("PartnershipScopeId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("partnership_scope_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("PartnershipContractId", "PartnershipScopeId");

                    b.HasIndex("PartnershipScopeId");

                    b.ToTable("partnership_scope_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipTerminationRequest", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<string>("FlowState")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("flow_state");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("LessonsLearned")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("lessons_learned");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("PartnerNotes")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("partner_notes");

                    b.Property<Guid>("PartnershipContractId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("partnership_contract_id");

                    b.Property<string>("ProgressSummary")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("progress_summary");

                    b.Property<string>("Successes")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("successes");

                    b.Property<DateTime>("TerminationDate")
                        .HasColumnType("datetime2")
                        .HasColumnName("termination_date");

                    b.Property<string>("TerminationReason")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("termination_reason");

                    b.HasKey("Id");

                    b.HasIndex("PartnershipContractId");

                    b.ToTable("partnership_termination_requests");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipType", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.HasKey("Id");

                    b.ToTable("partnership_types");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Pillar", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<int>("Order")
                        .HasColumnType("int")
                        .HasColumnName("order");

                    b.Property<Guid>("TournamentId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("tournament_id");

                    b.Property<double>("Weight")
                        .HasColumnType("float")
                        .HasColumnName("weight");

                    b.HasKey("Id");

                    b.HasIndex("TournamentId");

                    b.ToTable("pillars");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Plan", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("AssignedDepartmentId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("assigned_department_id");

                    b.Property<Guid?>("AssignedTeamId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("assigned_team_id");

                    b.Property<Guid?>("AssignedUserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("assigned_user_id");

                    b.Property<Guid>("CategoryId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("category_id");

                    b.Property<string>("Challenges")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("challenges");

                    b.Property<string>("Code")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("code");

                    b.Property<string>("CommunicationProcesses")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("communication_processes");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<string>("Description")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("description");

                    b.Property<string>("ExpectedBenefits")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("expected_benefits");

                    b.Property<string>("ExpectedOutputs")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("expected_outputs");

                    b.Property<string>("FinancialRequirements")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("financial_requirements");

                    b.Property<string>("FinancialStages")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("financial_stages");

                    b.Property<string>("FlowState")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("flow_state");

                    b.Property<DateTime?>("From")
                        .HasColumnType("datetime2")
                        .HasColumnName("from");

                    b.Property<string>("GovernmentDirections")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("government_directions");

                    b.Property<Guid?>("GovernmentStrategicGoalId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("government_strategic_goal_id");

                    b.Property<string>("ImplementationRequirement")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("implementation_requirement");

                    b.Property<string>("Initiatives")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("initiatives");

                    b.Property<int>("IsBudgetAllocated")
                        .HasColumnType("int")
                        .HasColumnName("is_budget_allocated");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("LessonsLearned")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("lessons_learned");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<string>("OtherPartners")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("other_partners");

                    b.Property<string>("Risks")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("risks");

                    b.Property<DateTime?>("To")
                        .HasColumnType("datetime2")
                        .HasColumnName("to");

                    b.Property<string>("WorkScope")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("work_scope");

                    b.Property<int>("Year")
                        .HasColumnType("int")
                        .HasColumnName("year");

                    b.HasKey("Id");

                    b.HasIndex("AssignedDepartmentId");

                    b.HasIndex("AssignedTeamId");

                    b.HasIndex("AssignedUserId");

                    b.HasIndex("CategoryId");

                    b.HasIndex("CreatedById");

                    b.HasIndex("GovernmentStrategicGoalId");

                    b.ToTable("plans");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanApproval", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("ApprovingDepartmentId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("approving_department_id");

                    b.Property<Guid>("ApprovingUserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("approving_user_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("note");

                    b.Property<Guid>("PlanId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("plan_id");

                    b.Property<string>("Type")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("type");

                    b.HasKey("Id");

                    b.ToTable("plan_approvals");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanCategory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<string>("DescriptionAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("description_ar");

                    b.Property<string>("DescriptionEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("description_en");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.HasKey("Id");

                    b.ToTable("plan_categories");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanDependency", b =>
                {
                    b.Property<Guid>("PrincipalPlanId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("principal_plan_id");

                    b.Property<Guid>("DependentPlanId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("dependent_plan_id");

                    b.Property<string>("Reason")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("reason");

                    b.Property<string>("Recommendations")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("recommendations");

                    b.Property<string>("Relation")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("relation");

                    b.HasKey("PrincipalPlanId", "DependentPlanId");

                    b.HasIndex("DependentPlanId");

                    b.ToTable("plan_dependencies");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanInput", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.HasKey("Id");

                    b.ToTable("plan_inputs");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanKpiLink", b =>
                {
                    b.Property<Guid>("PlanId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("plan_id");

                    b.Property<Guid>("KpiId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("kpi_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("PlanId", "KpiId");

                    b.HasIndex("KpiId");

                    b.ToTable("plan_kpi_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanMinistryStrategicGoalLink", b =>
                {
                    b.Property<Guid>("PlanId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("plan_id");

                    b.Property<Guid>("MinistryStrategicGoalId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("ministry_strategic_goal_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("PlanId", "MinistryStrategicGoalId");

                    b.HasIndex("MinistryStrategicGoalId");

                    b.ToTable("plan_ministry_strategic_goal_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanOperationLink", b =>
                {
                    b.Property<Guid>("PlanId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("plan_id");

                    b.Property<Guid>("OperationId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("operation_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("PlanId", "OperationId");

                    b.HasIndex("OperationId");

                    b.ToTable("plan_operation_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanPartnerLink", b =>
                {
                    b.Property<Guid>("PlanId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("plan_id");

                    b.Property<Guid>("PartnerId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("partner_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("PlanId", "PartnerId");

                    b.HasIndex("PartnerId");

                    b.ToTable("plan_partner_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanPlanInputLink", b =>
                {
                    b.Property<Guid>("PlanId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("plan_id");

                    b.Property<Guid>("PlanInputId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("plan_input_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<string>("Text")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("text");

                    b.HasKey("PlanId", "PlanInputId");

                    b.HasIndex("PlanInputId");

                    b.ToTable("plan_plan_input_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanPolicyLink", b =>
                {
                    b.Property<Guid>("PlanId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("plan_id");

                    b.Property<Guid>("PolicyId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("policy_id");

                    b.Property<int>("HasPlan")
                        .HasColumnType("int")
                        .HasColumnName("has_plan");

                    b.HasKey("PlanId", "PolicyId");

                    b.HasIndex("PolicyId");

                    b.ToTable("plan_policy_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanResource", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<int>("Count")
                        .HasColumnType("int")
                        .HasColumnName("count");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<string>("Name")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name");

                    b.Property<Guid>("PlanId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("plan_id");

                    b.HasKey("Id");

                    b.HasIndex("PlanId");

                    b.ToTable("plan_resources");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanStrategicGoalLink", b =>
                {
                    b.Property<Guid>("PlanId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("plan_id");

                    b.Property<Guid>("GoalId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("goal_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("PlanId", "GoalId");

                    b.HasIndex("GoalId");

                    b.ToTable("plan_strategic_goal_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanSubsubtask", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("Alternative")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("alternative");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<DateTime?>("From")
                        .HasColumnType("datetime2")
                        .HasColumnName("from");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NotPerformingReason")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("not_performing_reason");

                    b.Property<double?>("Progress")
                        .HasColumnType("float")
                        .HasColumnName("progress");

                    b.Property<Guid>("SubtaskId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("subtask_id");

                    b.Property<DateTime?>("To")
                        .HasColumnType("datetime2")
                        .HasColumnName("to");

                    b.HasKey("Id");

                    b.HasIndex("SubtaskId");

                    b.ToTable("plan_subsubtasks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanSubsubtaskApproval", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid>("ApprovingUserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("approving_user_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("note");

                    b.Property<Guid>("SubsubtaskId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("subsubtask_id");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("type");

                    b.HasKey("Id");

                    b.HasIndex("ApprovingUserId");

                    b.HasIndex("SubsubtaskId");

                    b.ToTable("plan_subsubtask_approvals");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanSubsubtaskLibraryFileLink", b =>
                {
                    b.Property<Guid>("PlanSubsubtaskId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("plan_subsubtask_id");

                    b.Property<Guid>("LibraryFileId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("library_file_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("PlanSubsubtaskId", "LibraryFileId");

                    b.HasIndex("LibraryFileId");

                    b.ToTable("plan_subsubtask_library_file_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanSubtask", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("AssignedDepartmentId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("assigned_department_id");

                    b.Property<Guid?>("AssignedTeamId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("assigned_team_id");

                    b.Property<Guid?>("AssignedUserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("assigned_user_id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<DateTime?>("From")
                        .HasColumnType("datetime2")
                        .HasColumnName("from");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<int>("IsRepeated")
                        .HasColumnType("int")
                        .HasColumnName("is_repeated");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<Guid?>("SecondaryAssignedDepartmentId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("secondary_assigned_department_id");

                    b.Property<Guid>("TaskId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("task_id");

                    b.Property<DateTime?>("To")
                        .HasColumnType("datetime2")
                        .HasColumnName("to");

                    b.HasKey("Id");

                    b.HasIndex("AssignedDepartmentId");

                    b.HasIndex("AssignedTeamId");

                    b.HasIndex("AssignedUserId");

                    b.HasIndex("SecondaryAssignedDepartmentId");

                    b.HasIndex("TaskId");

                    b.ToTable("plan_subtasks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanTask", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("AssignedDepartmentId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("assigned_department_id");

                    b.Property<Guid?>("AssignedTeamId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("assigned_team_id");

                    b.Property<Guid?>("AssignedUserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("assigned_user_id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<DateTime?>("From")
                        .HasColumnType("datetime2")
                        .HasColumnName("from");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<string>("OtherKpis")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("other_kpis");

                    b.Property<Guid>("PlanId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("plan_id");

                    b.Property<string>("Risks")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("risks");

                    b.Property<DateTime?>("To")
                        .HasColumnType("datetime2")
                        .HasColumnName("to");

                    b.Property<double>("Weight")
                        .HasColumnType("float")
                        .HasColumnName("weight");

                    b.HasKey("Id");

                    b.HasIndex("AssignedDepartmentId");

                    b.HasIndex("AssignedTeamId");

                    b.HasIndex("AssignedUserId");

                    b.HasIndex("PlanId");

                    b.ToTable("plan_tasks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanTaskKpiLink", b =>
                {
                    b.Property<Guid>("PlanTaskId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("plan_task_id");

                    b.Property<Guid>("KpiId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("kpi_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("PlanTaskId", "KpiId");

                    b.HasIndex("KpiId");

                    b.ToTable("plan_task_kpi_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanTaskOperationLink", b =>
                {
                    b.Property<Guid>("PlanTaskId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("plan_task_id");

                    b.Property<Guid>("OperationId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("operation_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("PlanTaskId", "OperationId");

                    b.HasIndex("OperationId");

                    b.ToTable("plan_task_operation_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Policy", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.HasKey("Id");

                    b.ToTable("policies");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Principle", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<string>("DescriptionAr")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description_ar");

                    b.Property<string>("DescriptionEn")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description_en");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<int>("Order")
                        .HasColumnType("int")
                        .HasColumnName("order");

                    b.Property<Guid>("StandardId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("standard_id");

                    b.Property<double>("Weight")
                        .HasColumnType("float")
                        .HasColumnName("weight");

                    b.HasKey("Id");

                    b.HasIndex("StandardId");

                    b.ToTable("principles");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.RiskModel.Risk", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid>("CategoryId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("category_id");

                    b.Property<string>("Causes")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("causes");

                    b.Property<string>("Code")
                        .HasMaxLength(128)
                        .HasColumnType("nvarchar(128)")
                        .HasColumnName("code");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<Guid>("DepartmentId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("department_id");

                    b.Property<string>("DescriptionAr")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description_ar");

                    b.Property<string>("DescriptionEn")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description_en");

                    b.Property<string>("ImpactDetails")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("impact_details");

                    b.Property<Guid>("ImpactId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("impact_id");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ManagementProcedureDetails")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("management_procedure_details");

                    b.Property<string>("ManagementProcedureSteps")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("management_procedure_steps");

                    b.Property<Guid>("ManagementStrategyId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("strategy_id");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<int>("Order")
                        .HasColumnType("int")
                        .HasColumnName("order");

                    b.Property<string>("Owner")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("owner");

                    b.Property<string>("ProbabilityDetails")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("probability_details");

                    b.Property<Guid>("ProbabilityId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("probability_id");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.HasIndex("DepartmentId");

                    b.HasIndex("ImpactId");

                    b.HasIndex("ManagementStrategyId");

                    b.HasIndex("ProbabilityId");

                    b.ToTable("risks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.RiskModel.RiskCategory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.HasKey("Id");

                    b.ToTable("risk_categories");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.RiskModel.RiskImpact", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("Color")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("color");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<int>("Degree")
                        .HasColumnType("int")
                        .HasColumnName("degree");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<string>("DescriptionAr")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description_ar");

                    b.Property<string>("DescriptionEn")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description_en");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.HasKey("Id");

                    b.ToTable("risk_impacts");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.RiskModel.RiskManagementProcedure", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description");

                    b.Property<DateTime>("ExpectedClosureTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("expected_closure_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("Owner")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("owner");

                    b.Property<Guid>("RiskId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("risk_id");

                    b.HasKey("Id");

                    b.HasIndex("RiskId");

                    b.ToTable("risk_management_procedures");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.RiskModel.RiskManagementProcedureLibraryFileLink", b =>
                {
                    b.Property<Guid>("ManagementProcedureId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("management_procedure_id");

                    b.Property<Guid>("LibraryFileId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("library_file_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("ManagementProcedureId", "LibraryFileId");

                    b.HasIndex("LibraryFileId");

                    b.ToTable("risk_management_procedure_library_file_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.RiskModel.RiskManagementStrategy", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.HasKey("Id");

                    b.ToTable("risk_management_strategies");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.RiskModel.RiskOperationLink", b =>
                {
                    b.Property<Guid>("RiskId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("risk_id");

                    b.Property<Guid>("OperationId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("operation_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("RiskId", "OperationId");

                    b.HasIndex("OperationId");

                    b.ToTable("risk_operation_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.RiskModel.RiskPlanLink", b =>
                {
                    b.Property<Guid>("RiskId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("risk_id");

                    b.Property<Guid>("PlanId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("plan_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("RiskId", "PlanId");

                    b.HasIndex("PlanId");

                    b.ToTable("risk_plan_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.RiskModel.RiskProbability", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("Color")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("color");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<int>("Degree")
                        .HasColumnType("int")
                        .HasColumnName("degree");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<string>("DescriptionAr")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description_ar");

                    b.Property<string>("DescriptionEn")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description_en");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.HasKey("Id");

                    b.ToTable("risk_probabilities");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.RiskModel.RiskStrategicGoalLink", b =>
                {
                    b.Property<Guid>("RiskId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("risk_id");

                    b.Property<Guid>("GoalId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("goal_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("RiskId", "GoalId");

                    b.HasIndex("GoalId");

                    b.ToTable("risk_strategic_goal_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Service", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("ApplicationStage")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("application_stage");

                    b.Property<string>("BusinessServiceInsurance")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("business_service_insurance");

                    b.Property<string>("CancelingReason")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("canceling_reason");

                    b.Property<string>("Categories")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("categories");

                    b.Property<string>("ClientCategories")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("client_categories");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<string>("DeliveryChannels")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("delivery_channels");

                    b.Property<string>("DeliveryStage")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("delivery_stage");

                    b.Property<string>("DeliveryTime")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("delivery_time");

                    b.Property<Guid?>("DepartmentId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("department_id");

                    b.Property<string>("DescriptionAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("description_ar");

                    b.Property<string>("DescriptionEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("description_en");

                    b.Property<string>("Development")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("development");

                    b.Property<string>("DevelopmentEffect")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("development_effect");

                    b.Property<string>("DevelopmentEntrances")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("development_entrances");

                    b.Property<string>("Duration")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("duration");

                    b.Property<string>("DurationType")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("duration_type");

                    b.Property<string>("ElectronicTransformationRatio")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("electronic_transformation_ratio");

                    b.Property<decimal>("Fee")
                        .HasColumnType("decimal(18,2)")
                        .HasColumnName("fee");

                    b.Property<string>("GovernmentServiceInsurance")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("government_service_insurance");

                    b.Property<string>("ImpactOnQualityOfLife")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("impact_on_quality_of_life");

                    b.Property<string>("IndividualServiceInsurance")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("individual_service_insurance");

                    b.Property<int>("IsActive")
                        .HasMaxLength(256)
                        .HasColumnType("int")
                        .HasColumnName("is_active");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<int>("IsElectronicallyConvertible")
                        .HasColumnType("int")
                        .HasColumnName("is_electronically_convertible");

                    b.Property<int>("IsLinkedWithOtherParties")
                        .HasColumnType("int")
                        .HasColumnName("is_linked_with_other_parties");

                    b.Property<int>("IsThereAPackage")
                        .HasColumnType("int")
                        .HasColumnName("is_there_a_package");

                    b.Property<string>("Limitation")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("limitation");

                    b.Property<string>("LinkedPartyName")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("linked_party_name");

                    b.Property<string>("LinkedPartyService")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("linked_party_service");

                    b.Property<string>("MainServiceNameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("main_service_name_ar");

                    b.Property<string>("MainServiceNameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("main_service_name_en");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("Ownership")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("ownership");

                    b.Property<string>("Package")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("package");

                    b.Property<string>("PaymentMethods")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("payment_methods");

                    b.Property<string>("Proactive")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("proactive");

                    b.Property<string>("ProactiveStandards")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("proactive_standards");

                    b.Property<string>("Procedures")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("procedures");

                    b.Property<string>("ProviderChannels")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("provider_channels");

                    b.Property<string>("QueryStage")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("query_stage");

                    b.Property<string>("ReceiptOfTheRequest")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("receipt_of_the_request");

                    b.Property<string>("SubServiceNameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("sub_service_name_ar");

                    b.Property<string>("SubServiceNameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("sub_service_name_en");

                    b.Property<string>("SupplementaryServiceNameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("supplementary_service_name_ar");

                    b.Property<string>("SupplementaryServiceNameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("supplementary_service_name_en");

                    b.Property<string>("Types")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("types");

                    b.HasKey("Id");

                    b.ToTable("services");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.ServiceCategory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.HasKey("Id");

                    b.ToTable("service_categories");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.ServiceCategoryLink", b =>
                {
                    b.Property<Guid>("ServiceId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("service_Id");

                    b.Property<Guid>("CategoryId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("category_Id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("ServiceId", "CategoryId");

                    b.HasIndex("CategoryId");

                    b.ToTable("service_category_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.ServiceDeliveryChannel", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.HasKey("Id");

                    b.ToTable("service_delivery_channels");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.ServiceDepartmentLink", b =>
                {
                    b.Property<Guid>("ServiceId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("service_Id");

                    b.Property<Guid>("DepartmentId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("department_Id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid>("Id")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.HasKey("ServiceId", "DepartmentId");

                    b.HasIndex("DepartmentId");

                    b.ToTable("service_department_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.ServicePartnerLink", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<string>("OtherPartner")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("other_partner");

                    b.Property<string>("PartnerDetails")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("partner_details");

                    b.Property<Guid?>("PartnerId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("partner_Id");

                    b.Property<Guid>("ServiceId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("service_Id");

                    b.HasKey("Id");

                    b.HasIndex("PartnerId");

                    b.HasIndex("ServiceId");

                    b.ToTable("service_partner_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.ServiceProvider", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<string>("Name")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("name");

                    b.Property<string>("Position")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("position");

                    b.Property<Guid>("ServiceId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("service_id");

                    b.Property<string>("Site")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("site");

                    b.Property<string>("TrainingHours")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("training_hours");

                    b.Property<string>("TrainingType")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("training_type");

                    b.HasKey("Id");

                    b.HasIndex("ServiceId");

                    b.ToTable("service_providers");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.ServiceProviderChannel", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.HasKey("Id");

                    b.ToTable("service_provider_channels");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.ServiceServiceDeliveryChannelLink", b =>
                {
                    b.Property<Guid>("ServiceId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("service_id");

                    b.Property<Guid>("ServiceDeliveryChannelId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("service_delivery_channel_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("ServiceId", "ServiceDeliveryChannelId");

                    b.HasIndex("ServiceDeliveryChannelId");

                    b.ToTable("service_service_delivery_channel_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.ServiceServiceProviderChannelLink", b =>
                {
                    b.Property<Guid>("ServiceId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("service_id");

                    b.Property<Guid>("ServiceProviderChannelId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("service_provider_channel_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("ServiceId", "ServiceProviderChannelId");

                    b.HasIndex("ServiceProviderChannelId");

                    b.ToTable("service_service_provider_channel_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Standard", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<int>("Order")
                        .HasColumnType("int")
                        .HasColumnName("order");

                    b.Property<Guid>("PillarId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("pillar_id");

                    b.Property<Guid?>("PresentationLibraryFileId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("presentation_library_file_id");

                    b.Property<string>("TeamAchievementSummary")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("team_achievement_summary");

                    b.Property<Guid?>("TeamFormationLibraryFileId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("team_formation_library_file_id");

                    b.Property<double>("Weight")
                        .HasColumnType("float")
                        .HasColumnName("weight");

                    b.HasKey("Id");

                    b.HasIndex("PillarId");

                    b.HasIndex("PresentationLibraryFileId");

                    b.HasIndex("TeamFormationLibraryFileId");

                    b.ToTable("standards");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StandardCapabilityLink", b =>
                {
                    b.Property<Guid>("StandardId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("standard_id");

                    b.Property<Guid>("CapabilityId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("capability_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<int>("Order")
                        .HasColumnType("int")
                        .HasColumnName("order");

                    b.HasKey("StandardId", "CapabilityId");

                    b.HasIndex("CapabilityId");

                    b.ToTable("standard_capability_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StandardKpiLink", b =>
                {
                    b.Property<Guid>("StandardId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("standard_id");

                    b.Property<Guid>("KpiId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("kpi_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<int>("Order")
                        .HasColumnType("int")
                        .HasColumnName("order");

                    b.HasKey("StandardId", "KpiId");

                    b.HasIndex("KpiId");

                    b.ToTable("standard_kpi_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StandardLibraryFileLink", b =>
                {
                    b.Property<Guid>("StandardId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("standard_id");

                    b.Property<Guid>("LibraryFileId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("library_file_id");

                    b.Property<string>("Description")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description");

                    b.Property<int>("Order")
                        .HasColumnType("int")
                        .HasColumnName("order");

                    b.HasKey("StandardId", "LibraryFileId");

                    b.HasIndex("LibraryFileId");

                    b.ToTable("standard_library_file_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StandardSubtask", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<DateTime>("From")
                        .HasColumnType("datetime2")
                        .HasColumnName("from");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<double?>("Progress")
                        .HasColumnType("float")
                        .HasColumnName("progress");

                    b.Property<string>("ProgressDescription")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("progress_description");

                    b.Property<Guid>("TaskId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("task_id");

                    b.Property<DateTime>("To")
                        .HasColumnType("datetime2")
                        .HasColumnName("to");

                    b.HasKey("Id");

                    b.HasIndex("TaskId");

                    b.ToTable("standard_subtasks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StandardSubtaskApproval", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid>("ApprovingUserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("approving_user_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<string>("Note")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("note");

                    b.Property<Guid>("SubtaskId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("subtask_id");

                    b.Property<string>("Type")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("type");

                    b.HasKey("Id");

                    b.HasIndex("ApprovingUserId");

                    b.HasIndex("SubtaskId");

                    b.ToTable("standard_subtask_approvals");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StandardSubtaskComment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("Content")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("content");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<Guid>("SubtaskId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("subtask_id");

                    b.HasKey("Id");

                    b.HasIndex("CreatedById");

                    b.HasIndex("SubtaskId");

                    b.ToTable("standard_subtask_comments");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StandardSubtaskLibraryFileLink", b =>
                {
                    b.Property<Guid>("SubtaskId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("subtask_id");

                    b.Property<Guid>("LibraryFileId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("library_file_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("SubtaskId", "LibraryFileId");

                    b.HasIndex("LibraryFileId");

                    b.ToTable("standard_subtask_library_file_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StandardSubtaskStandardUserLink", b =>
                {
                    b.Property<Guid>("SubtaskId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("subtask_id");

                    b.Property<Guid>("StandardId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("standard_id");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("user_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("SubtaskId", "StandardId", "UserId");

                    b.HasIndex("StandardId", "UserId");

                    b.ToTable("standard_subtask_standard_user_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StandardTask", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<DateTime>("From")
                        .HasColumnType("datetime2")
                        .HasColumnName("from");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<Guid>("StandardId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("standard_id");

                    b.Property<DateTime>("To")
                        .HasColumnType("datetime2")
                        .HasColumnName("to");

                    b.HasKey("Id");

                    b.HasIndex("StandardId");

                    b.ToTable("standard_tasks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StandardUserLink", b =>
                {
                    b.Property<Guid>("StandardId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("standard_id");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("user_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<string>("Roles")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("roles");

                    b.HasKey("StandardId", "UserId");

                    b.HasIndex("UserId");

                    b.ToTable("standard_user_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StatisticalReport", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<string>("Cycle")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("cycle");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("InitialYear")
                        .HasColumnType("int")
                        .HasColumnName("initial_year");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<int>("IsPublished")
                        .HasColumnType("int")
                        .HasColumnName("is_published");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("name_en");

                    b.HasKey("Id");

                    b.ToTable("statistical_reports");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StatisticalReportCategory", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<string>("NameAr")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("name_en");

                    b.Property<Guid?>("ParentId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("parent_id");

                    b.Property<Guid>("ReportId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("report_id");

                    b.HasKey("Id");

                    b.HasIndex("ParentId");

                    b.HasIndex("ReportId");

                    b.ToTable("statistical_report_categories");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StatisticalReportCategoryDepartmentLink", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid>("CategoryId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("category_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid>("DepartmentId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("department_id");

                    b.HasKey("Id");

                    b.HasIndex("CategoryId");

                    b.HasIndex("DepartmentId");

                    b.ToTable("statistical_report_category_department_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StatisticalReportCategoryResult", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid>("CategoryDepartmentLinkId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("category_department_link_id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<int>("IsLocked")
                        .HasColumnType("int")
                        .HasColumnName("is_locked");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<int>("Period")
                        .HasColumnType("int")
                        .HasColumnName("period");

                    b.Property<double?>("Value")
                        .HasColumnType("float")
                        .HasColumnName("value");

                    b.Property<int>("Year")
                        .HasColumnType("int")
                        .HasColumnName("year");

                    b.HasKey("Id");

                    b.HasIndex("CategoryDepartmentLinkId");

                    b.ToTable("statistical_report_category_result");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StrategicGoal", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("Category")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("category");

                    b.Property<string>("Code")
                        .HasMaxLength(100)
                        .HasColumnType("nvarchar(100)")
                        .HasColumnName("code");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<string>("DescriptionAr")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description_ar");

                    b.Property<string>("DescriptionEn")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description_en");

                    b.Property<int?>("FromYear")
                        .HasColumnType("int")
                        .HasColumnName("from_year");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<int?>("Order")
                        .HasColumnType("int")
                        .HasColumnName("order");

                    b.Property<int?>("ToYear")
                        .HasColumnType("int")
                        .HasColumnName("to_year");

                    b.Property<double?>("Weight")
                        .HasColumnType("float")
                        .HasColumnName("weight");

                    b.HasKey("Id");

                    b.ToTable("strategic_goals");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StrategicPerspective", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<Guid?>("PlanId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("plan_id");

                    b.HasKey("Id");

                    b.HasIndex("PlanId");

                    b.ToTable("strategic_perspectives");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StrategicPerspectiveGoalsLink", b =>
                {
                    b.Property<Guid?>("PerspectiveId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("perspective_id");

                    b.Property<Guid?>("GoalId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("goal_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("PerspectiveId", "GoalId");

                    b.HasIndex("GoalId");

                    b.ToTable("strategic_perspective_goals_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StrategicPillar", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<Guid?>("PlanId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("plan_id");

                    b.HasKey("Id");

                    b.HasIndex("PlanId");

                    b.ToTable("strategic_pillars");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StrategicPillarGoalsLink", b =>
                {
                    b.Property<Guid>("PillarId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("pillar_id");

                    b.Property<Guid>("GoalId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("goal_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("PillarId", "GoalId");

                    b.HasIndex("GoalId");

                    b.ToTable("strategic_pillar_goals_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StrategicPlan", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("EndYear")
                        .HasColumnType("int")
                        .HasColumnName("end_year");

                    b.Property<bool>("IsActive")
                        .HasColumnType("bit")
                        .HasColumnName("is_active");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("MissionAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("mission_ar");

                    b.Property<string>("MissionEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("mission_en");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<int>("StartYear")
                        .HasColumnType("int")
                        .HasColumnName("start_year");

                    b.Property<string>("VisionAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("vision_ar");

                    b.Property<string>("VisionEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("vision_en");

                    b.HasKey("Id");

                    b.ToTable("strategic_plans");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StrategicValue", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<Guid>("PlanId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("plan_id");

                    b.HasKey("Id");

                    b.HasIndex("PlanId");

                    b.ToTable("strategic_values");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.SuccessFactor", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<double?>("Weight")
                        .HasColumnType("float")
                        .HasColumnName("weight");

                    b.HasKey("Id");

                    b.ToTable("success_factors");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Team", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("nameAr");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("nameEn");

                    b.HasKey("Id");

                    b.ToTable("teams");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.TeamUserLink", b =>
                {
                    b.Property<Guid>("TeamId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("team_id");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("user_id");

                    b.Property<string>("Experience")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("experience");

                    b.Property<string>("Position")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("position");

                    b.Property<string>("Role")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("role");

                    b.HasKey("TeamId", "UserId");

                    b.HasIndex("UserId");

                    b.ToTable("team_user_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Tournament", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDefault")
                        .HasColumnType("int")
                        .HasColumnName("is_default");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<int>("IsHidden")
                        .HasColumnType("int")
                        .HasColumnName("is_hidden");

                    b.Property<Guid?>("LibraryFileId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("library_file_id");

                    b.Property<string>("LogoFileName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("logo_file_name");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<int>("Order")
                        .HasColumnType("int")
                        .HasColumnName("order");

                    b.Property<string>("Version")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("version");

                    b.Property<int>("Year")
                        .HasColumnType("int")
                        .HasColumnName("year");

                    b.Property<string>("Years")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("years");

                    b.HasKey("Id");

                    b.HasIndex("LibraryFileId");

                    b.ToTable("tournaments");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.TrainingProgram", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("Hours")
                        .HasColumnType("int")
                        .HasColumnName("hours");

                    b.Property<Guid>("InnovatorId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("innovator_id");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<int>("Year")
                        .HasColumnType("int")
                        .HasColumnName("year");

                    b.HasKey("Id");

                    b.HasIndex("InnovatorId");

                    b.ToTable("training_programs");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.User", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<int>("AccessFailedCount")
                        .HasColumnType("int")
                        .HasColumnName("access_fail_count");

                    b.Property<string>("ConcurrencyStamp")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("concurrency_stamp");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<string>("Email")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("email");

                    b.Property<bool>("EmailConfirmed")
                        .HasColumnType("bit")
                        .HasColumnName("email_confirmed");

                    b.Property<string>("EmployeeNumber")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("employee_number");

                    b.Property<string>("Gender")
                        .HasMaxLength(16)
                        .HasColumnType("nvarchar(16)")
                        .HasColumnName("gender");

                    b.Property<string>("ImageFileName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("image_file_name");

                    b.Property<int>("IsDeactivated")
                        .HasColumnType("int")
                        .HasColumnName("is_deactivated");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<bool>("LockoutEnabled")
                        .HasColumnType("bit")
                        .HasColumnName("lockout_enabled");

                    b.Property<DateTimeOffset?>("LockoutEnd")
                        .HasColumnType("datetimeoffset")
                        .HasColumnName("lockout_end");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.Property<string>("NormalizedEmail")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("normalized_email");

                    b.Property<string>("NormalizedUserName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("normalized_username");

                    b.Property<string>("PasswordHash")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("password_hash");

                    b.Property<string>("PhoneNumber")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("phone_number");

                    b.Property<bool>("PhoneNumberConfirmed")
                        .HasColumnType("bit")
                        .HasColumnName("phone_number_confirmed");

                    b.Property<string>("Rank")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("rank");

                    b.Property<string>("SecurityStamp")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("security_stamp");

                    b.Property<string>("Status")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("status");

                    b.Property<bool>("TwoFactorEnabled")
                        .HasColumnType("bit")
                        .HasColumnName("two_factor_enabled");

                    b.Property<string>("Type")
                        .HasMaxLength(32)
                        .HasColumnType("nvarchar(32)")
                        .HasColumnName("type");

                    b.Property<string>("UserName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("username");

                    b.HasKey("Id");

                    b.ToTable("users", (string)null);
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.UserRequest", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("Content")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("content");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsClosed")
                        .HasColumnType("int")
                        .HasColumnName("is_closed");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<Guid?>("KpiId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("kpi_id");

                    b.Property<Guid?>("LibraryFileId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("library_file_id");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<Guid?>("OperationId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("operation_id");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("user_id");

                    b.HasKey("Id");

                    b.HasIndex("KpiId");

                    b.HasIndex("LibraryFileId");

                    b.HasIndex("OperationId");

                    b.HasIndex("UserId");

                    b.ToTable("users_requests");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.UserRequestComment", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("Content")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("content");

                    b.Property<Guid?>("CreatedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("created_by_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<Guid?>("DeletedById")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("deleted_by_id");

                    b.Property<DateTime?>("DeletionTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("deletion_time");

                    b.Property<int>("IsDeleted")
                        .HasColumnType("int")
                        .HasColumnName("is_deleted");

                    b.Property<string>("ModificationHistory")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("modification_history");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("user_id");

                    b.Property<Guid>("UserRequestId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("user_request_id");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.HasIndex("UserRequestId");

                    b.ToTable("user_request_comments");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Year", b =>
                {
                    b.Property<int>("Value")
                        .HasColumnType("int")
                        .HasColumnName("value");

                    b.HasKey("Value");

                    b.ToTable("years");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.Identity.Role", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("ConcurrencyStamp")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("concurrency_stamp");

                    b.Property<string>("DisplayName")
                        .HasMaxLength(256)
                        .HasColumnType("nvarchar(256)")
                        .HasColumnName("display_name");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("name");

                    b.Property<string>("NormalizedName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("normalized_name");

                    b.HasKey("Id");

                    b.ToTable("roles", (string)null);
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.Identity.RoleClaim", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("claim_type");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("claim_value");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("role_id");

                    b.HasKey("Id");

                    b.ToTable("role_claims", (string)null);
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.Identity.UserClaim", b =>
                {
                    b.Property<int>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("id")
                        .HasAnnotation("SqlServer:ValueGenerationStrategy", SqlServerValueGenerationStrategy.IdentityColumn);

                    b.Property<string>("ClaimType")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("claim_type");

                    b.Property<string>("ClaimValue")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("claim_value");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("user_id");

                    b.HasKey("Id");

                    b.HasIndex("UserId");

                    b.ToTable("user_claims", (string)null);
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.Identity.UserLogin", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("login_provider");

                    b.Property<string>("ProviderDisplayName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("provider_display_name");

                    b.Property<string>("ProviderKey")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("provider_key");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("user_id");

                    b.HasKey("Id");

                    b.ToTable("user_logins", (string)null);
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.Identity.UserRole", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<Guid>("RoleId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("role_id");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("user_id");

                    b.HasKey("Id");

                    b.HasIndex("RoleId");

                    b.ToTable("user_roles", (string)null);
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.Identity.UserToken", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<string>("LoginProvider")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("login_provider");

                    b.Property<string>("Name")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("name");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("user_id");

                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("value");

                    b.HasKey("Id");

                    b.ToTable("user_tokens");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.Permission.Permission", b =>
                {
                    b.Property<string>("Id")
                        .HasMaxLength(512)
                        .HasColumnType("nvarchar(512)")
                        .HasColumnName("id");

                    b.HasKey("Id");

                    b.ToTable("permissions");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.Permission.PermissionGroup", b =>
                {
                    b.Property<Guid>("Id")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.Property<string>("DescriptionAr")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description_ar");

                    b.Property<string>("DescriptionEn")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("description_en");

                    b.Property<string>("NameAr")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_ar");

                    b.Property<string>("NameEn")
                        .HasMaxLength(1024)
                        .HasColumnType("nvarchar(1024)")
                        .HasColumnName("name_en");

                    b.HasKey("Id");

                    b.ToTable("permission_groups");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.Permission.PermissionGroupUserLink", b =>
                {
                    b.Property<Guid>("GroupId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("group_id");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("user_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("GroupId", "UserId");

                    b.HasIndex("UserId");

                    b.ToTable("permission_group_user_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.Permission.PermissionOverrider", b =>
                {
                    b.Property<string>("PermissionId")
                        .HasColumnType("nvarchar(512)")
                        .HasColumnName("permission_id");

                    b.Property<string>("OverriderId")
                        .HasColumnType("nvarchar(512)")
                        .HasColumnName("overrider_id");

                    b.HasKey("PermissionId", "OverriderId");

                    b.HasIndex("OverriderId");

                    b.ToTable("permission_overriders");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.Permission.PermissionPermissionGroupLink", b =>
                {
                    b.Property<string>("PermissionId")
                        .HasColumnType("nvarchar(512)")
                        .HasColumnName("permission_id");

                    b.Property<Guid>("GroupId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("group_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("PermissionId", "GroupId");

                    b.HasIndex("GroupId");

                    b.ToTable("permission_permission_group_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.Permission.PermissionUserLink", b =>
                {
                    b.Property<string>("PermissionId")
                        .HasColumnType("nvarchar(512)")
                        .HasColumnName("permission_id");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("user_id");

                    b.Property<DateTime>("CreationTime")
                        .HasColumnType("datetime2")
                        .HasColumnName("creation_time");

                    b.HasKey("PermissionId", "UserId");

                    b.HasIndex("UserId");

                    b.ToTable("permission_user_links");
                });

            modelBuilder.Entity("Injaz.Core.Models.Misc.IntWrapper", b =>
                {
                    b.Property<int>("Value")
                        .ValueGeneratedOnAdd()
                        .HasColumnType("int")
                        .HasColumnName("value");

                    b.HasKey("Value");

                    b.ToSqlQuery("workaround for table create/drop");
                });

            modelBuilder.Entity("Injaz.Core.Models.Misc.ModificationRecord", b =>
                {
                    b.Property<string>("Notes")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("notes");

                    b.Property<DateTime>("Time")
                        .HasColumnType("datetime2")
                        .HasColumnName("time");

                    b.Property<string>("UserFullName")
                        .HasColumnType("nvarchar(max)")
                        .HasColumnName("user_full_name");

                    b.Property<Guid>("UserId")
                        .HasColumnType("uniqueidentifier")
                        .HasColumnName("user_id");

                    b.ToSqlQuery("workaround for table create/drop");
                });

            modelBuilder.Entity("Injaz.Core.Models.Misc.StringWrapper", b =>
                {
                    b.Property<string>("Value")
                        .HasColumnType("nvarchar(450)")
                        .HasColumnName("value");

                    b.HasKey("Value");

                    b.ToSqlQuery("workaround for table create/drop");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Award", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Innovator", "Innovator")
                        .WithMany("Awards")
                        .HasForeignKey("InnovatorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Innovator");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Benchmark", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Department", "Department")
                        .WithMany()
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.User", "FirstApprovalUser")
                        .WithMany()
                        .HasForeignKey("FirstApprovalUserId");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.PartnerModel.Partner", "Partner")
                        .WithMany("Benchmarks")
                        .HasForeignKey("PartnerId");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.User", "SecondApprovalUser")
                        .WithMany()
                        .HasForeignKey("SecondApprovalUserId");

                    b.Navigation("Department");

                    b.Navigation("FirstApprovalUser");

                    b.Navigation("Partner");

                    b.Navigation("SecondApprovalUser");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.BenchmarkBenchmarkRequestReasonLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Benchmark", "Benchmark")
                        .WithMany("RequestReasonLinks")
                        .HasForeignKey("BenchmarkId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.BenchmarkRequestReason", "Reason")
                        .WithMany("BenchmarkLinks")
                        .HasForeignKey("ReasonId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Benchmark");

                    b.Navigation("Reason");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.BenchmarkBenchmarkSelectionReasonLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Benchmark", "Benchmark")
                        .WithMany("SelectionReasonLinks")
                        .HasForeignKey("BenchmarkId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.BenchmarkSelectionReason", "Reason")
                        .WithMany("BenchmarkLinks")
                        .HasForeignKey("ReasonId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Benchmark");

                    b.Navigation("Reason");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.BenchmarkKpiResultLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Benchmark", "Benchmark")
                        .WithMany("KpiResultLinks")
                        .HasForeignKey("BenchmarkId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.KpiResult", "Result")
                        .WithMany("BenchmarkLinks")
                        .HasForeignKey("ResultId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Benchmark");

                    b.Navigation("Result");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.BenchmarkLibraryFileLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Benchmark", "Benchmark")
                        .WithMany("LibraryFileLinks")
                        .HasForeignKey("BenchmarkId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.LibraryFile", "LibraryFile")
                        .WithMany("BenchmarkLinks")
                        .HasForeignKey("LibraryFileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Benchmark");

                    b.Navigation("LibraryFile");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.BenchmarkOperationLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Benchmark", "Benchmark")
                        .WithMany("OperationLinks")
                        .HasForeignKey("BenchmarkId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Operation", "Operation")
                        .WithMany("BenchmarkLinks")
                        .HasForeignKey("OperationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Benchmark");

                    b.Navigation("Operation");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.BenchmarkOtherManagement", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Benchmark", "Benchmark")
                        .WithMany("OtherManagements")
                        .HasForeignKey("BenchmarkId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Benchmark");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.BenchmarkStrategicGoalLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Benchmark", "Benchmark")
                        .WithMany("GoalLinks")
                        .HasForeignKey("BenchmarkId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.StrategicGoal", "Goal")
                        .WithMany("BenchmarkLinks")
                        .HasForeignKey("GoalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Benchmark");

                    b.Navigation("Goal");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.BenchmarkVisitor", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Benchmark", "Benchmark")
                        .WithMany("Visitors")
                        .HasForeignKey("BenchmarkId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Benchmark");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Capability", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.CapabilityType", "Type")
                        .WithMany()
                        .HasForeignKey("TypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Type");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.CapabilityKpiLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Capability", "Capability")
                        .WithMany("KpiLinks")
                        .HasForeignKey("CapabilityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Kpi", "Kpi")
                        .WithMany("CapabilityLinks")
                        .HasForeignKey("KpiId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Capability");

                    b.Navigation("Kpi");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.CapabilityLibraryFileLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Capability", "Capability")
                        .WithMany("LibraryFileLinks")
                        .HasForeignKey("CapabilityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.LibraryFile", "LibraryFile")
                        .WithMany("CapabilityLinks")
                        .HasForeignKey("LibraryFileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Capability");

                    b.Navigation("LibraryFile");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Department", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.OrganizationType", "OrganizationType")
                        .WithMany("Departments")
                        .HasForeignKey("OrganizationTypeId");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Department", "ParentDepartment")
                        .WithMany("Children")
                        .HasForeignKey("ParentDepartmentId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("OrganizationType");

                    b.Navigation("ParentDepartment");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.DepartmentKpiLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Department", "Department")
                        .WithMany("KpiLinks")
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Kpi", "Kpi")
                        .WithMany("DepartmentLinks")
                        .HasForeignKey("KpiId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Department");

                    b.Navigation("Kpi");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.DepartmentUserLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Department", "Department")
                        .WithMany("UserLinks")
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.User", "User")
                        .WithMany("DepartmentLinks")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Department");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.EvaluationModel.EvaluationScoreBand", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.EvaluationModel.Evaluation", "Evaluation")
                        .WithMany("ScoreBands")
                        .HasForeignKey("EvaluationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Evaluation");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.EvaluationModel.EvaluationStandard", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.EvaluationModel.Evaluation", "Evaluation")
                        .WithMany("Standards")
                        .HasForeignKey("EvaluationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Evaluation");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.EvaluationModel.EvaluationStandardRecord", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.EvaluationModel.EvaluationInstance", "Instance")
                        .WithMany("Records")
                        .HasForeignKey("InstanceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Instance");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.FlowTransaction", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Department", "Department")
                        .WithMany()
                        .HasForeignKey("DepartmentId");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Department");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.ImprovementOpportunity", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Department", "AssignedDepartment")
                        .WithMany()
                        .HasForeignKey("AssignedDepartmentId");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Team", "AssignedTeam")
                        .WithMany()
                        .HasForeignKey("AssignedTeamId");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.User", "AssignedUser")
                        .WithMany()
                        .HasForeignKey("AssignedUserId");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.ImprovementOpportunityInputCategory", "ImprovementOpportunityInputCategory")
                        .WithMany("ImprovementOpportunities")
                        .HasForeignKey("InputCategoryId");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Department", "OwningDepartment")
                        .WithMany("OwnedOpportunities")
                        .HasForeignKey("OwningDepartmentId");

                    b.Navigation("AssignedDepartment");

                    b.Navigation("AssignedTeam");

                    b.Navigation("AssignedUser");

                    b.Navigation("ImprovementOpportunityInputCategory");

                    b.Navigation("OwningDepartment");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.ImprovementOpportunityInputSourceLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.ImprovementOpportunity", "Opportunity")
                        .WithMany("InputSourceLinks")
                        .HasForeignKey("OpportunityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.ImprovementOpportunityInputSource", "Source")
                        .WithMany("ImprovementOpportunityLinks")
                        .HasForeignKey("SourceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Opportunity");

                    b.Navigation("Source");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.ImprovementOpportunityLibraryFileLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.ImprovementOpportunity", "ImprovementOpportunity")
                        .WithMany("LibraryFileLinks")
                        .HasForeignKey("ImprovementOpportunityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.LibraryFile", "LibraryFile")
                        .WithMany("ImprovementOpportunityLinks")
                        .HasForeignKey("LibraryFileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ImprovementOpportunity");

                    b.Navigation("LibraryFile");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.ImprovementOpportunityOperationLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.ImprovementOpportunity", "ImprovementOpportunity")
                        .WithMany("OperationLinks")
                        .HasForeignKey("ImprovementOpportunityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Operation", "Operation")
                        .WithMany("ImprovementOpportunityLinks")
                        .HasForeignKey("OperationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ImprovementOpportunity");

                    b.Navigation("Operation");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.ImprovementOpportunityPrincipleLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.ImprovementOpportunity", "ImprovementOpportunity")
                        .WithMany("PrincipleLinks")
                        .HasForeignKey("ImprovementOpportunityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Principle", "Principle")
                        .WithMany()
                        .HasForeignKey("PrincipleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ImprovementOpportunity");

                    b.Navigation("Principle");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.ImprovementOpportunityStandardLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.ImprovementOpportunity", "ImprovementOpportunity")
                        .WithMany("StandardLinks")
                        .HasForeignKey("ImprovementOpportunityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Standard", "Standard")
                        .WithMany()
                        .HasForeignKey("StandardId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ImprovementOpportunity");

                    b.Navigation("Standard");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Innovation", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Innovator", "Innovator")
                        .WithMany("Innovations")
                        .HasForeignKey("InnovatorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Innovator");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.InnovationActivityLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Activity", "Activity")
                        .WithMany("InnovationLinks")
                        .HasForeignKey("ActivityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Innovation", "Innovation")
                        .WithMany("ActivityLinks")
                        .HasForeignKey("InnovationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Activity");

                    b.Navigation("Innovation");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Kpi", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.KpiBalancedBehaviorCard", "BalancedBehaviorCard")
                        .WithMany("Kpis")
                        .HasForeignKey("BalancedBehaviorCardId");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Department", "OwningDepartment")
                        .WithMany("OwnedKpis")
                        .HasForeignKey("OwningDepartmentId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.KpiType", "Type")
                        .WithMany("Kpis")
                        .HasForeignKey("TypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("BalancedBehaviorCard");

                    b.Navigation("OwningDepartment");

                    b.Navigation("Type");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiBenchmark", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Kpi", "Kpi")
                        .WithMany("Benchmarks")
                        .HasForeignKey("KpiId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.LibraryFile", "LibraryFile")
                        .WithMany("KpiBenchmarks")
                        .HasForeignKey("LibraryFileId");

                    b.Navigation("Kpi");

                    b.Navigation("LibraryFile");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiDynamicDataEntryRequest", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.KpiResultPeriod", "Period")
                        .WithMany()
                        .HasForeignKey("PeriodId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Period");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiDynamicDataEntryRequestLibraryFileLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.LibraryFile", "LibraryFile")
                        .WithMany("KpiDynamicDataEntryRequestLinks")
                        .HasForeignKey("LibraryFileId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.KpiDynamicDataEntryRequest", "Request")
                        .WithMany("FileLinks")
                        .HasForeignKey("RequestId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("LibraryFile");

                    b.Navigation("Request");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiKpiTagLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Kpi", "Kpi")
                        .WithMany("TagLinks")
                        .HasForeignKey("KpiId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.KpiTag", "Tag")
                        .WithMany("KpiLinks")
                        .HasForeignKey("TagId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Kpi");

                    b.Navigation("Tag");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResult", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Department", "Department")
                        .WithMany("KpiResults")
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Kpi", "Kpi")
                        .WithMany("Results")
                        .HasForeignKey("KpiId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.User", "ModifiedBy")
                        .WithMany()
                        .HasForeignKey("ModifiedById")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Department");

                    b.Navigation("Kpi");

                    b.Navigation("ModifiedBy");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultAttachment", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.KpiResultDataEntryResponse", "KpiResultDataEntryResponse")
                        .WithMany("Attachments")
                        .HasForeignKey("KpiResultDataEntryResponseId");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.KpiResult", "KpiResult")
                        .WithMany("Attachments")
                        .HasForeignKey("KpiResultId");

                    b.Navigation("KpiResult");

                    b.Navigation("KpiResultDataEntryResponse");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultBreakdown", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.KpiResult", "Result")
                        .WithMany("Breakdowns")
                        .HasForeignKey("ResultId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.KpiResultSubcategory", "Subcategory")
                        .WithMany("Breakdowns")
                        .HasForeignKey("SubcategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Result");

                    b.Navigation("Subcategory");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultCapability", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.KpiResultDataEntryResponsePeriod", "KpiResultDataEntryResponsePeriod")
                        .WithMany("Capabilities")
                        .HasForeignKey("KpiResultDataEntryResponsePeriodId");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.KpiResultPeriod", "KpiResultPeriod")
                        .WithMany("Capabilities")
                        .HasForeignKey("KpiResultPeriodId")
                        .OnDelete(DeleteBehavior.SetNull);

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.KpiResultCapabilityType", "Type")
                        .WithMany()
                        .HasForeignKey("TypeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("KpiResultDataEntryResponsePeriod");

                    b.Navigation("KpiResultPeriod");

                    b.Navigation("Type");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultCapabilityLibraryFileLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.KpiResultCapability", "KpiResultCapability")
                        .WithMany("LibraryFileLinks")
                        .HasForeignKey("CapabilityId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.LibraryFile", "LibraryFile")
                        .WithMany("KpiResultCapabilityLinks")
                        .HasForeignKey("LibraryFileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("KpiResultCapability");

                    b.Navigation("LibraryFile");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultDataEntryRequestDepartmentLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Department", "Department")
                        .WithMany()
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.KpiResultDataEntryRequest", "Request")
                        .WithMany("DepartmentLinks")
                        .HasForeignKey("RequestId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Department");

                    b.Navigation("Request");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultDataEntryRequestKpiLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Kpi", "Kpi")
                        .WithMany()
                        .HasForeignKey("KpiId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.KpiResultDataEntryRequest", "Request")
                        .WithMany("KpiLinks")
                        .HasForeignKey("RequestId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Kpi");

                    b.Navigation("Request");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultDataEntryRequestNotification", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.KpiResultDataEntryRequest", "Request")
                        .WithMany("Notifications")
                        .HasForeignKey("RequestId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Request");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultDataEntryResponse", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.KpiResultDataEntryRequest", "Request")
                        .WithMany("Responses")
                        .HasForeignKey("RequestId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.KpiResult", "Result")
                        .WithMany()
                        .HasForeignKey("ResultId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Request");

                    b.Navigation("Result");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultDataEntryResponsePeriod", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.KpiResultDataEntryResponse", "Response")
                        .WithMany("Periods")
                        .HasForeignKey("ResponseId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Response");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultDataEntryResponseTransfer", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.KpiResultDataEntryResponse", "Response")
                        .WithMany("Transfers")
                        .HasForeignKey("ResponseId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("Response");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultDataEntryResponseTransferUserLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.KpiResultDataEntryResponseTransfer", "Transfer")
                        .WithMany("UserLinks")
                        .HasForeignKey("TransferId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Transfer");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultPeriod", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.KpiResult", "KpiResult")
                        .WithMany("Periods")
                        .HasForeignKey("KpiResultId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("KpiResult");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultPeriodBreakdown", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.KpiResultBreakdown", "Breakdown")
                        .WithMany("Values")
                        .HasForeignKey("BreakdownId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.KpiResultPeriod", "Period")
                        .WithMany("Breakdowns")
                        .HasForeignKey("PeriodId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("Breakdown");

                    b.Navigation("Period");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultRequest", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.KpiResult", "KpiResult")
                        .WithMany("Requests")
                        .HasForeignKey("KpiResultId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("KpiResult");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultRequestComment", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.KpiResultRequest", "Request")
                        .WithMany("Comments")
                        .HasForeignKey("RequestId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Request");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultSubcategory", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.KpiResultCategory", "Category")
                        .WithMany("Subcategories")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Category");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiStrategicGoalLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Kpi", "Kpi")
                        .WithMany("StrategicGoalLinks")
                        .HasForeignKey("KpiId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.StrategicGoal", "StrategicGoal")
                        .WithMany("KpiLinks")
                        .HasForeignKey("StrategicGoalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Kpi");

                    b.Navigation("StrategicGoal");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.LibraryFile", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.User", "Owner")
                        .WithMany()
                        .HasForeignKey("OwnerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Owner");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.LibraryFileLibraryTagLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.LibraryFile", "LibraryFile")
                        .WithMany("TagLinks")
                        .HasForeignKey("LibraryFileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.LibraryTag", "Tag")
                        .WithMany("LibraryFileLinks")
                        .HasForeignKey("TagId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("LibraryFile");

                    b.Navigation("Tag");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.LinkedApplication", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.NotificationUserLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Notification", "Notification")
                        .WithMany("UserLinks")
                        .HasForeignKey("NotificationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.User", "User")
                        .WithMany("NotificationLinks")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Notification");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Operation", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.LibraryFile", "MainFlowChartFile")
                        .WithMany("Operations")
                        .HasForeignKey("MainFLowChartId");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Department", "OwnerDepartment")
                        .WithMany("Operations")
                        .HasForeignKey("OwnerDepartmentId");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Operation", "ParentOperation")
                        .WithMany("Children")
                        .HasForeignKey("ParentId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("MainFlowChartFile");

                    b.Navigation("OwnerDepartment");

                    b.Navigation("ParentOperation");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OperationEnhancement", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.LibraryFile", "BusinessModelFile")
                        .WithMany("EnhancementsAsBusinessModelFiles")
                        .HasForeignKey("BusinessModelFileId");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.LibraryFile", "FlowChartFile")
                        .WithMany("EnhancementsAsFlowChartFile")
                        .HasForeignKey("FlowChartFileId");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Operation", "Operation")
                        .WithMany("Enhancements")
                        .HasForeignKey("OperationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.OperationEnhancementType", "Type")
                        .WithMany("OperationEnhancements")
                        .HasForeignKey("TypeId");

                    b.Navigation("BusinessModelFile");

                    b.Navigation("FlowChartFile");

                    b.Navigation("Operation");

                    b.Navigation("Type");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OperationEnhancementFileLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.OperationEnhancement", "Enhancement")
                        .WithMany("FileLinks")
                        .HasForeignKey("EnhancementId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.LibraryFile", "LibraryFile")
                        .WithMany()
                        .HasForeignKey("FileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Enhancement");

                    b.Navigation("LibraryFile");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OperationExecutor", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Operation", "Operation")
                        .WithMany("Executors")
                        .HasForeignKey("OperationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.PartnerModel.Partner", null)
                        .WithMany("OperationExecutors")
                        .HasForeignKey("PartnerId");

                    b.Navigation("Operation");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OperationFormFileLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.LibraryFile", "LibraryFile")
                        .WithMany()
                        .HasForeignKey("FileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Operation", "Operation")
                        .WithMany("FormFileLinks")
                        .HasForeignKey("OperationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("LibraryFile");

                    b.Navigation("Operation");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OperationKpiLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Kpi", "Kpi")
                        .WithMany("OperationLinks")
                        .HasForeignKey("KpiId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Operation", "Operation")
                        .WithMany("KpiLinks")
                        .HasForeignKey("OperationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Kpi");

                    b.Navigation("Operation");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OperationPartnerLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Operation", "Operation")
                        .WithMany("PartnerLinks")
                        .HasForeignKey("OperationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.PartnerModel.Partner", "Partner")
                        .WithMany("OperationLinks")
                        .HasForeignKey("PartnerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Operation");

                    b.Navigation("Partner");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OperationPolicyLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Operation", "Operation")
                        .WithMany("PolicyLinks")
                        .HasForeignKey("OperationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Policy", "Policy")
                        .WithMany("OperationPolicyLinks")
                        .HasForeignKey("PolicyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Operation");

                    b.Navigation("Policy");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OperationProcedure", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.LibraryFile", "FlowchartFile")
                        .WithMany("OperationProcedures")
                        .HasForeignKey("FlowchartFileId");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Operation", "Operation")
                        .WithMany("Procedures")
                        .HasForeignKey("OperationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("FlowchartFile");

                    b.Navigation("Operation");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OperationProcedureKpiLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Kpi", "Kpi")
                        .WithMany("OperationProcedureLinks")
                        .HasForeignKey("KpiId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.OperationProcedure", "Procedure")
                        .WithMany("KpiLinks")
                        .HasForeignKey("ProcedureId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Kpi");

                    b.Navigation("Procedure");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OperationProcedureStep", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.OperationProcedure", "Procedure")
                        .WithMany("Steps")
                        .HasForeignKey("ProcedureId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.LibraryFile", "UsedModelFile")
                        .WithMany("OperationProcedureSteps")
                        .HasForeignKey("UsedModelFileId");

                    b.Navigation("Procedure");

                    b.Navigation("UsedModelFile");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OperationRuleAndRegulationLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Operation", "Operation")
                        .WithMany("RuleAndRegulationLinks")
                        .HasForeignKey("OperationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.OperationRuleAndRegulation", "OperationRuleAndRegulation")
                        .WithMany("OperationLinks")
                        .HasForeignKey("RuleAndRegulationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Operation");

                    b.Navigation("OperationRuleAndRegulation");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OperationSpecificationLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Operation", "Operation")
                        .WithMany("SpecificationLinks")
                        .HasForeignKey("OperationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.OperationSpecification", "Specification")
                        .WithMany("OperationSpecificationLinks")
                        .HasForeignKey("SpecificationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Operation");

                    b.Navigation("Specification");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OperationStrategicGoalLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.StrategicGoal", "StrategicGoal")
                        .WithMany("OperationLinks")
                        .HasForeignKey("GoalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Operation", "Operation")
                        .WithMany("GoalLinks")
                        .HasForeignKey("OperationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Operation");

                    b.Navigation("StrategicGoal");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OperationSuccessFactorLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Operation", "Operation")
                        .WithMany("SuccessFactorOperationLinks")
                        .HasForeignKey("OperationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.SuccessFactor", "SuccessFactor")
                        .WithMany("OperationLinks")
                        .HasForeignKey("SuccessFactorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Operation");

                    b.Navigation("SuccessFactor");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipActivity", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipActivityCommunicationTool", "CommunicationTool")
                        .WithMany("Activities")
                        .HasForeignKey("CommunicationToolId");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipContract", "PartnershipContract")
                        .WithMany("Activities")
                        .HasForeignKey("PartnershipContractId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CommunicationTool");

                    b.Navigation("PartnershipContract");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipActivityPeriod", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipActivity", "PartnershipActivity")
                        .WithMany("Periods")
                        .HasForeignKey("PartnershipActivityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PartnershipActivity");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipActivityPeriodAttachment", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipActivityPeriod", "Period")
                        .WithMany("Attachments")
                        .HasForeignKey("PartnershipActivityPeriodId");

                    b.Navigation("Period");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipContract", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.LibraryFile", "AgreementFile")
                        .WithMany("PartnershipContracts")
                        .HasForeignKey("AgreementFileId");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Department", "Department")
                        .WithMany("PartnershipContracts")
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipField", "Field")
                        .WithMany("PartnershipContracts")
                        .HasForeignKey("FieldId");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.PartnerModel.Partner", "Partner")
                        .WithMany("PartnershipContracts")
                        .HasForeignKey("PartnerId");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipType", "PartnershipType")
                        .WithMany("PartnershipContracts")
                        .HasForeignKey("PartnershipTypeId");

                    b.Navigation("AgreementFile");

                    b.Navigation("Department");

                    b.Navigation("Field");

                    b.Navigation("Partner");

                    b.Navigation("PartnershipType");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipFrameworkLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipContract", "PartnershipContract")
                        .WithMany("PartnershipFrameworkLinks")
                        .HasForeignKey("PartnershipContractId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipFramework", "PartnershipFramework")
                        .WithMany("PartnershipContractLinks")
                        .HasForeignKey("PartnershipFrameworkId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PartnershipContract");

                    b.Navigation("PartnershipFramework");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipGoal", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipContract", "PartnershipContract")
                        .WithMany("Goals")
                        .HasForeignKey("PartnershipContractId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.StrategicGoal", "StrategicGoal")
                        .WithMany("PartnershipGoals")
                        .HasForeignKey("StrategicGoalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PartnershipContract");

                    b.Navigation("StrategicGoal");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipGoalActivity", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipGoal", "PartnershipGoal")
                        .WithMany("Activities")
                        .HasForeignKey("PartnershipGoalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PartnershipGoal");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipGoalActivityKpiLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Kpi", "Kpi")
                        .WithMany("PartnershipGoalActivityLinks")
                        .HasForeignKey("KpiId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipGoalActivity", "PartnershipGoalActivity")
                        .WithMany("KpiLinks")
                        .HasForeignKey("PartnershipGoalActivityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Kpi");

                    b.Navigation("PartnershipGoalActivity");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipGoalInitiative", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipGoal", "PartnershipGoal")
                        .WithMany("Initiatives")
                        .HasForeignKey("PartnershipGoalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PartnershipGoal");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipGoalNationalAgendaLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.NationalAgenda", "NationalAgenda")
                        .WithMany("PartnershipGoalLinks")
                        .HasForeignKey("NationalAgendaId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipGoal", "PartnershipGoal")
                        .WithMany("NationalAgendaLinks")
                        .HasForeignKey("PartnershipGoalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("NationalAgenda");

                    b.Navigation("PartnershipGoal");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipGoalOperationLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Operation", "Operation")
                        .WithMany("PartnershipGoalLinks")
                        .HasForeignKey("OperationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipGoal", "PartnershipGoal")
                        .WithMany("OperationLinks")
                        .HasForeignKey("PartnershipGoalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Operation");

                    b.Navigation("PartnershipGoal");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipGoalServiceLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipGoal", "PartnershipGoal")
                        .WithMany("ServiceLinks")
                        .HasForeignKey("PartnershipGoalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Service", "Service")
                        .WithMany("PartnershipGoalLinks")
                        .HasForeignKey("ServiceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PartnershipGoal");

                    b.Navigation("Service");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipPartnerEvaluation", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipContract", "PartnershipContract")
                        .WithMany("PartnershipPartnerEvaluations")
                        .HasForeignKey("PartnershipContractId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PartnershipContract");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipPartnerStandardLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.PartnerModel.PartnerStandard", "PartnerStandard")
                        .WithMany("PartnershipContractLinks")
                        .HasForeignKey("PartnerStandardId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipContract", "PartnershipContract")
                        .WithMany("PartnershipPartnerStandardLinks")
                        .HasForeignKey("PartnershipContractId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PartnerStandard");

                    b.Navigation("PartnershipContract");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipScope", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipField", "PartnershipField")
                        .WithMany("PartnershipScopes")
                        .HasForeignKey("PartnershipFieldId")
                        .OnDelete(DeleteBehavior.NoAction);

                    b.Navigation("PartnershipField");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipScopeLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipContract", "PartnershipContract")
                        .WithMany("PartnershipScopeLinks")
                        .HasForeignKey("PartnershipContractId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipScope", "PartnershipScope")
                        .WithMany("PartnershipContractLinks")
                        .HasForeignKey("PartnershipScopeId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PartnershipContract");

                    b.Navigation("PartnershipScope");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipTerminationRequest", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipContract", "PartnershipContract")
                        .WithMany("TerminationRequests")
                        .HasForeignKey("PartnershipContractId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PartnershipContract");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Pillar", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Tournament", "Tournament")
                        .WithMany("Pillars")
                        .HasForeignKey("TournamentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Tournament");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Plan", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Department", "AssignedDepartment")
                        .WithMany("Plans")
                        .HasForeignKey("AssignedDepartmentId");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Team", "AssignedTeam")
                        .WithMany("Plans")
                        .HasForeignKey("AssignedTeamId");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.User", "AssignedUser")
                        .WithMany("Plans")
                        .HasForeignKey("AssignedUserId");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.PlanCategory", "Category")
                        .WithMany("Plans")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.GovernmentStrategicGoal", "GovernmentStrategicGoal")
                        .WithMany("Plans")
                        .HasForeignKey("GovernmentStrategicGoalId");

                    b.Navigation("AssignedDepartment");

                    b.Navigation("AssignedTeam");

                    b.Navigation("AssignedUser");

                    b.Navigation("Category");

                    b.Navigation("CreatedBy");

                    b.Navigation("GovernmentStrategicGoal");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanDependency", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Plan", "DependentPlan")
                        .WithMany("Principals")
                        .HasForeignKey("DependentPlanId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Plan", "PrincipalPlan")
                        .WithMany("Dependencies")
                        .HasForeignKey("PrincipalPlanId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("DependentPlan");

                    b.Navigation("PrincipalPlan");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanKpiLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Kpi", "Kpi")
                        .WithMany()
                        .HasForeignKey("KpiId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Plan", "Plan")
                        .WithMany("KpiLinks")
                        .HasForeignKey("PlanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Kpi");

                    b.Navigation("Plan");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanMinistryStrategicGoalLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.MinistryStrategicGoal", "MinistryStrategicGoal")
                        .WithMany()
                        .HasForeignKey("MinistryStrategicGoalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Plan", "Plan")
                        .WithMany("MinistryStrategicGoalLinks")
                        .HasForeignKey("PlanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("MinistryStrategicGoal");

                    b.Navigation("Plan");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanOperationLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Operation", "Operation")
                        .WithMany("PlanLinks")
                        .HasForeignKey("OperationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Plan", "Plan")
                        .WithMany("OperationLinks")
                        .HasForeignKey("PlanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Operation");

                    b.Navigation("Plan");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanPartnerLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.PartnerModel.Partner", "Partner")
                        .WithMany("PlanLinks")
                        .HasForeignKey("PartnerId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Plan", "Plan")
                        .WithMany("PartnerLinks")
                        .HasForeignKey("PlanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Partner");

                    b.Navigation("Plan");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanPlanInputLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Plan", "Plan")
                        .WithMany("InputLinks")
                        .HasForeignKey("PlanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.PlanInput", "PlanInput")
                        .WithMany()
                        .HasForeignKey("PlanInputId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Plan");

                    b.Navigation("PlanInput");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanPolicyLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Plan", "Plan")
                        .WithMany("PolicyLinks")
                        .HasForeignKey("PlanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Policy", "Policy")
                        .WithMany()
                        .HasForeignKey("PolicyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Plan");

                    b.Navigation("Policy");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanResource", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Plan", "Plan")
                        .WithMany("Resources")
                        .HasForeignKey("PlanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Plan");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanStrategicGoalLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.StrategicGoal", "Goal")
                        .WithMany()
                        .HasForeignKey("GoalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Plan", "Plan")
                        .WithMany("StrategicGoalLinks")
                        .HasForeignKey("PlanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Goal");

                    b.Navigation("Plan");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanSubsubtask", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.PlanSubtask", "Subtask")
                        .WithMany("Subsubtasks")
                        .HasForeignKey("SubtaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Subtask");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanSubsubtaskApproval", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.User", "ApprovingUser")
                        .WithMany()
                        .HasForeignKey("ApprovingUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.PlanSubsubtask", "Subsubtask")
                        .WithMany("Approvals")
                        .HasForeignKey("SubsubtaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ApprovingUser");

                    b.Navigation("Subsubtask");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanSubsubtaskLibraryFileLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.LibraryFile", "LibraryFile")
                        .WithMany("PlanSubsubtaskLinks")
                        .HasForeignKey("LibraryFileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.PlanSubsubtask", "PlanSubsubtask")
                        .WithMany("LibraryFileLinks")
                        .HasForeignKey("PlanSubsubtaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("LibraryFile");

                    b.Navigation("PlanSubsubtask");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanSubtask", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Department", "AssignedDepartment")
                        .WithMany("Subtasks")
                        .HasForeignKey("AssignedDepartmentId");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Team", "AssignedTeam")
                        .WithMany("Subtasks")
                        .HasForeignKey("AssignedTeamId");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.User", "AssignedUser")
                        .WithMany("Subtasks")
                        .HasForeignKey("AssignedUserId");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Department", "SecondaryAssignedDepartment")
                        .WithMany()
                        .HasForeignKey("SecondaryAssignedDepartmentId");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.PlanTask", "Task")
                        .WithMany("Subtasks")
                        .HasForeignKey("TaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AssignedDepartment");

                    b.Navigation("AssignedTeam");

                    b.Navigation("AssignedUser");

                    b.Navigation("SecondaryAssignedDepartment");

                    b.Navigation("Task");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanTask", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Department", "AssignedDepartment")
                        .WithMany("Tasks")
                        .HasForeignKey("AssignedDepartmentId");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Team", "AssignedTeam")
                        .WithMany("Tasks")
                        .HasForeignKey("AssignedTeamId");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.User", "AssignedUser")
                        .WithMany("Tasks")
                        .HasForeignKey("AssignedUserId");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Plan", "Plan")
                        .WithMany("Tasks")
                        .HasForeignKey("PlanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("AssignedDepartment");

                    b.Navigation("AssignedTeam");

                    b.Navigation("AssignedUser");

                    b.Navigation("Plan");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanTaskKpiLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Kpi", "Kpi")
                        .WithMany("PlanTaskLinks")
                        .HasForeignKey("KpiId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.PlanTask", "PlanTask")
                        .WithMany("KpiLinks")
                        .HasForeignKey("PlanTaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Kpi");

                    b.Navigation("PlanTask");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanTaskOperationLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Operation", "Operation")
                        .WithMany("PlanTaskLinks")
                        .HasForeignKey("OperationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.PlanTask", "PlanTask")
                        .WithMany("OperationLinks")
                        .HasForeignKey("PlanTaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Operation");

                    b.Navigation("PlanTask");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Principle", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Standard", "Standard")
                        .WithMany("Principles")
                        .HasForeignKey("StandardId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Standard");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.RiskModel.Risk", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.RiskModel.RiskCategory", "Category")
                        .WithMany("Risks")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Department", "Department")
                        .WithMany("Risks")
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.RiskModel.RiskImpact", "Impact")
                        .WithMany("Risks")
                        .HasForeignKey("ImpactId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.RiskModel.RiskManagementStrategy", "ManagementStrategy")
                        .WithMany("Risks")
                        .HasForeignKey("ManagementStrategyId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.RiskModel.RiskProbability", "Probability")
                        .WithMany("Risks")
                        .HasForeignKey("ProbabilityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Category");

                    b.Navigation("Department");

                    b.Navigation("Impact");

                    b.Navigation("ManagementStrategy");

                    b.Navigation("Probability");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.RiskModel.RiskManagementProcedure", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.RiskModel.Risk", "Risk")
                        .WithMany("ManagementProcedures")
                        .HasForeignKey("RiskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Risk");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.RiskModel.RiskManagementProcedureLibraryFileLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.LibraryFile", "LibraryFile")
                        .WithMany("RiskManagementProcedureLinks")
                        .HasForeignKey("LibraryFileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.RiskModel.RiskManagementProcedure", "ManagementProcedure")
                        .WithMany("LibraryFileLinks")
                        .HasForeignKey("ManagementProcedureId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("LibraryFile");

                    b.Navigation("ManagementProcedure");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.RiskModel.RiskOperationLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Operation", "Operation")
                        .WithMany("RiskLinks")
                        .HasForeignKey("OperationId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.RiskModel.Risk", "Risk")
                        .WithMany("OperationLinks")
                        .HasForeignKey("RiskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Operation");

                    b.Navigation("Risk");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.RiskModel.RiskPlanLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Plan", "Plan")
                        .WithMany("RiskLinks")
                        .HasForeignKey("PlanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.RiskModel.Risk", "Risk")
                        .WithMany("PlanLinks")
                        .HasForeignKey("RiskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Plan");

                    b.Navigation("Risk");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.RiskModel.RiskStrategicGoalLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.StrategicGoal", "Goal")
                        .WithMany("RiskLinks")
                        .HasForeignKey("GoalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.RiskModel.Risk", "Risk")
                        .WithMany("GoalLinks")
                        .HasForeignKey("RiskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Goal");

                    b.Navigation("Risk");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.ServiceCategoryLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.ServiceCategory", "Category")
                        .WithMany("ServiceLinks")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Service", "Service")
                        .WithMany("CategoryLinks")
                        .HasForeignKey("ServiceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Category");

                    b.Navigation("Service");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.ServiceDepartmentLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Department", "Department")
                        .WithMany("ServiceLinks")
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Service", "Service")
                        .WithMany("DepartmentLinks")
                        .HasForeignKey("ServiceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Department");

                    b.Navigation("Service");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.ServicePartnerLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.PartnerModel.Partner", "Partner")
                        .WithMany("ServiceLinks")
                        .HasForeignKey("PartnerId");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Service", "Service")
                        .WithMany("PartnerLinks")
                        .HasForeignKey("ServiceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Partner");

                    b.Navigation("Service");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.ServiceProvider", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Service", "Service")
                        .WithMany("Providers")
                        .HasForeignKey("ServiceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Service");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.ServiceServiceDeliveryChannelLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.ServiceDeliveryChannel", "ServiceDeliveryChannel")
                        .WithMany("ServiceLinks")
                        .HasForeignKey("ServiceDeliveryChannelId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Service", "Service")
                        .WithMany("ServiceDeliveryChannelLinks")
                        .HasForeignKey("ServiceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Service");

                    b.Navigation("ServiceDeliveryChannel");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.ServiceServiceProviderChannelLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Service", "Service")
                        .WithMany("ServiceProviderChannelLinks")
                        .HasForeignKey("ServiceId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.ServiceProviderChannel", "ServiceProviderChannel")
                        .WithMany("ServiceLinks")
                        .HasForeignKey("ServiceProviderChannelId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Service");

                    b.Navigation("ServiceProviderChannel");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Standard", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Pillar", "Pillar")
                        .WithMany("Standards")
                        .HasForeignKey("PillarId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.LibraryFile", "PresentationLibraryFile")
                        .WithMany("StandardPresentations")
                        .HasForeignKey("PresentationLibraryFileId");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.LibraryFile", "TeamFormationLibraryFile")
                        .WithMany("StandardTeamFormations")
                        .HasForeignKey("TeamFormationLibraryFileId");

                    b.Navigation("Pillar");

                    b.Navigation("PresentationLibraryFile");

                    b.Navigation("TeamFormationLibraryFile");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StandardCapabilityLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Capability", "Capability")
                        .WithMany("StandardLinks")
                        .HasForeignKey("CapabilityId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Standard", "Standard")
                        .WithMany("CapabilityLinks")
                        .HasForeignKey("StandardId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Capability");

                    b.Navigation("Standard");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StandardKpiLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Kpi", "Kpi")
                        .WithMany("StandardLinks")
                        .HasForeignKey("KpiId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Standard", "Standard")
                        .WithMany("KpiLinks")
                        .HasForeignKey("StandardId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Kpi");

                    b.Navigation("Standard");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StandardLibraryFileLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.LibraryFile", "LibraryFile")
                        .WithMany("StandardLinks")
                        .HasForeignKey("LibraryFileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Standard", "Standard")
                        .WithMany("LibraryFileLinks")
                        .HasForeignKey("StandardId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("LibraryFile");

                    b.Navigation("Standard");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StandardSubtask", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.StandardTask", "Task")
                        .WithMany("Subtasks")
                        .HasForeignKey("TaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Task");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StandardSubtaskApproval", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.User", "ApprovingUser")
                        .WithMany()
                        .HasForeignKey("ApprovingUserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.StandardSubtask", "Subtask")
                        .WithMany("Approvals")
                        .HasForeignKey("SubtaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("ApprovingUser");

                    b.Navigation("Subtask");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StandardSubtaskComment", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.User", "CreatedBy")
                        .WithMany()
                        .HasForeignKey("CreatedById");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.StandardSubtask", "Subtask")
                        .WithMany("Comments")
                        .HasForeignKey("SubtaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CreatedBy");

                    b.Navigation("Subtask");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StandardSubtaskLibraryFileLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.LibraryFile", "LibraryFile")
                        .WithMany("StandardSubtaskLinks")
                        .HasForeignKey("LibraryFileId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.StandardSubtask", "Subtask")
                        .WithMany("LibraryFileLinks")
                        .HasForeignKey("SubtaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("LibraryFile");

                    b.Navigation("Subtask");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StandardSubtaskStandardUserLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.StandardSubtask", "Subtask")
                        .WithMany("StandardUserLinks")
                        .HasForeignKey("SubtaskId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.StandardUserLink", "StandardUserLink")
                        .WithMany("StandardSubtaskUserLinks")
                        .HasForeignKey("StandardId", "UserId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("StandardUserLink");

                    b.Navigation("Subtask");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StandardTask", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Standard", "Standard")
                        .WithMany("Tasks")
                        .HasForeignKey("StandardId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Standard");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StandardUserLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Standard", "Standard")
                        .WithMany("UserLinks")
                        .HasForeignKey("StandardId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Standard");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StatisticalReportCategory", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.StatisticalReportCategory", "Parent")
                        .WithMany("Children")
                        .HasForeignKey("ParentId");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.StatisticalReport", "Report")
                        .WithMany("Categories")
                        .HasForeignKey("ReportId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Parent");

                    b.Navigation("Report");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StatisticalReportCategoryDepartmentLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.StatisticalReportCategory", "Category")
                        .WithMany("DepartmentLinks")
                        .HasForeignKey("CategoryId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Department", "Department")
                        .WithMany("StatisticalReportCategoryLinks")
                        .HasForeignKey("DepartmentId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Category");

                    b.Navigation("Department");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StatisticalReportCategoryResult", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.StatisticalReportCategoryDepartmentLink", "CategoryDepartmentLink")
                        .WithMany("Results")
                        .HasForeignKey("CategoryDepartmentLinkId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("CategoryDepartmentLink");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StrategicPerspective", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.StrategicPlan", "StrategicPlan")
                        .WithMany("StrategicPerspectives")
                        .HasForeignKey("PlanId");

                    b.Navigation("StrategicPlan");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StrategicPerspectiveGoalsLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.StrategicGoal", "StrategicGoal")
                        .WithMany("StrategicPerspectiveGoalsLinks")
                        .HasForeignKey("GoalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.StrategicPerspective", "StrategicPerspective")
                        .WithMany()
                        .HasForeignKey("PerspectiveId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("StrategicGoal");

                    b.Navigation("StrategicPerspective");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StrategicPillar", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.StrategicPlan", "StrategicPlan")
                        .WithMany("StrategicPillars")
                        .HasForeignKey("PlanId");

                    b.Navigation("StrategicPlan");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StrategicPillarGoalsLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.StrategicGoal", "StrategicGoal")
                        .WithMany("StrategicPillarGoalsLinks")
                        .HasForeignKey("GoalId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.StrategicPillar", "StrategicPillar")
                        .WithMany()
                        .HasForeignKey("PillarId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("StrategicGoal");

                    b.Navigation("StrategicPillar");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StrategicValue", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.StrategicPlan", "StrategicPlan")
                        .WithMany("StrategicValues")
                        .HasForeignKey("PlanId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("StrategicPlan");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.TeamUserLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Team", "Team")
                        .WithMany("UserLinks")
                        .HasForeignKey("TeamId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.User", "User")
                        .WithMany("TeamLinks")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Team");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Tournament", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.LibraryFile", "LibraryFile")
                        .WithMany("Tournaments")
                        .HasForeignKey("LibraryFileId");

                    b.Navigation("LibraryFile");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.TrainingProgram", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Innovator", "Innovator")
                        .WithMany("TrainingPrograms")
                        .HasForeignKey("InnovatorId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Innovator");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.UserRequest", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Kpi", "Kpi")
                        .WithMany("UserRequests")
                        .HasForeignKey("KpiId");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.LibraryFile", "LibraryFile")
                        .WithMany("UserRequests")
                        .HasForeignKey("LibraryFileId");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.Operation", "Operation")
                        .WithMany("UserRequests")
                        .HasForeignKey("OperationId");

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.User", "User")
                        .WithMany()
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Kpi");

                    b.Navigation("LibraryFile");

                    b.Navigation("Operation");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.UserRequestComment", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.User", "User")
                        .WithMany("UserRequestComments")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.UserRequest", "UserRequest")
                        .WithMany("RequestComments")
                        .HasForeignKey("UserRequestId")
                        .OnDelete(DeleteBehavior.NoAction)
                        .IsRequired();

                    b.Navigation("User");

                    b.Navigation("UserRequest");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.Identity.UserClaim", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.App.User", "User")
                        .WithMany("Claims")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("User");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.Identity.UserRole", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.Identity.Role", "Role")
                        .WithMany()
                        .HasForeignKey("RoleId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Role");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.Permission.PermissionGroupUserLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.Permission.PermissionGroup", "PermissionGroup")
                        .WithMany("UserLinks")
                        .HasForeignKey("GroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.User", "User")
                        .WithMany("PermissionGroupLinks")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("PermissionGroup");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.Permission.PermissionOverrider", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.Permission.Permission", "Overrider")
                        .WithMany("OverriddenPermissions")
                        .HasForeignKey("OverriderId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.Permission.Permission", "Permission")
                        .WithMany("Overriders")
                        .HasForeignKey("PermissionId")
                        .OnDelete(DeleteBehavior.Restrict)
                        .IsRequired();

                    b.Navigation("Overrider");

                    b.Navigation("Permission");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.Permission.PermissionPermissionGroupLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.Permission.PermissionGroup", "Group")
                        .WithMany("PermissionLinks")
                        .HasForeignKey("GroupId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.Permission.Permission", "Permission")
                        .WithMany("GroupLinks")
                        .HasForeignKey("PermissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Group");

                    b.Navigation("Permission");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.Permission.PermissionUserLink", b =>
                {
                    b.HasOne("Injaz.Core.Models.DomainClasses.Permission.Permission", "Permission")
                        .WithMany("UserLinks")
                        .HasForeignKey("PermissionId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.HasOne("Injaz.Core.Models.DomainClasses.App.User", "User")
                        .WithMany("PermissionLinks")
                        .HasForeignKey("UserId")
                        .OnDelete(DeleteBehavior.Cascade)
                        .IsRequired();

                    b.Navigation("Permission");

                    b.Navigation("User");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Activity", b =>
                {
                    b.Navigation("InnovationLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Benchmark", b =>
                {
                    b.Navigation("GoalLinks");

                    b.Navigation("KpiResultLinks");

                    b.Navigation("LibraryFileLinks");

                    b.Navigation("OperationLinks");

                    b.Navigation("OtherManagements");

                    b.Navigation("RequestReasonLinks");

                    b.Navigation("SelectionReasonLinks");

                    b.Navigation("Visitors");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.BenchmarkRequestReason", b =>
                {
                    b.Navigation("BenchmarkLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.BenchmarkSelectionReason", b =>
                {
                    b.Navigation("BenchmarkLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Capability", b =>
                {
                    b.Navigation("KpiLinks");

                    b.Navigation("LibraryFileLinks");

                    b.Navigation("StandardLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Department", b =>
                {
                    b.Navigation("Children");

                    b.Navigation("KpiLinks");

                    b.Navigation("KpiResults");

                    b.Navigation("Operations");

                    b.Navigation("OwnedKpis");

                    b.Navigation("OwnedOpportunities");

                    b.Navigation("PartnershipContracts");

                    b.Navigation("Plans");

                    b.Navigation("Risks");

                    b.Navigation("ServiceLinks");

                    b.Navigation("StatisticalReportCategoryLinks");

                    b.Navigation("Subtasks");

                    b.Navigation("Tasks");

                    b.Navigation("UserLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.EvaluationModel.Evaluation", b =>
                {
                    b.Navigation("ScoreBands");

                    b.Navigation("Standards");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.EvaluationModel.EvaluationInstance", b =>
                {
                    b.Navigation("Records");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.GovernmentStrategicGoal", b =>
                {
                    b.Navigation("Plans");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.ImprovementOpportunity", b =>
                {
                    b.Navigation("InputSourceLinks");

                    b.Navigation("LibraryFileLinks");

                    b.Navigation("OperationLinks");

                    b.Navigation("PrincipleLinks");

                    b.Navigation("StandardLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.ImprovementOpportunityInputCategory", b =>
                {
                    b.Navigation("ImprovementOpportunities");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.ImprovementOpportunityInputSource", b =>
                {
                    b.Navigation("ImprovementOpportunityLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Innovation", b =>
                {
                    b.Navigation("ActivityLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Innovator", b =>
                {
                    b.Navigation("Awards");

                    b.Navigation("Innovations");

                    b.Navigation("TrainingPrograms");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Kpi", b =>
                {
                    b.Navigation("Benchmarks");

                    b.Navigation("CapabilityLinks");

                    b.Navigation("DepartmentLinks");

                    b.Navigation("OperationLinks");

                    b.Navigation("OperationProcedureLinks");

                    b.Navigation("PartnershipGoalActivityLinks");

                    b.Navigation("PlanTaskLinks");

                    b.Navigation("Results");

                    b.Navigation("StandardLinks");

                    b.Navigation("StrategicGoalLinks");

                    b.Navigation("TagLinks");

                    b.Navigation("UserRequests");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiBalancedBehaviorCard", b =>
                {
                    b.Navigation("Kpis");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiDynamicDataEntryRequest", b =>
                {
                    b.Navigation("FileLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResult", b =>
                {
                    b.Navigation("Attachments");

                    b.Navigation("BenchmarkLinks");

                    b.Navigation("Breakdowns");

                    b.Navigation("Periods");

                    b.Navigation("Requests");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultBreakdown", b =>
                {
                    b.Navigation("Values");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultCapability", b =>
                {
                    b.Navigation("LibraryFileLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultCategory", b =>
                {
                    b.Navigation("Subcategories");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultDataEntryRequest", b =>
                {
                    b.Navigation("DepartmentLinks");

                    b.Navigation("KpiLinks");

                    b.Navigation("Notifications");

                    b.Navigation("Responses");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultDataEntryResponse", b =>
                {
                    b.Navigation("Attachments");

                    b.Navigation("Periods");

                    b.Navigation("Transfers");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultDataEntryResponsePeriod", b =>
                {
                    b.Navigation("Capabilities");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultDataEntryResponseTransfer", b =>
                {
                    b.Navigation("UserLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultPeriod", b =>
                {
                    b.Navigation("Breakdowns");

                    b.Navigation("Capabilities");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultRequest", b =>
                {
                    b.Navigation("Comments");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiResultSubcategory", b =>
                {
                    b.Navigation("Breakdowns");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiTag", b =>
                {
                    b.Navigation("KpiLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.KpiType", b =>
                {
                    b.Navigation("Kpis");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.LibraryFile", b =>
                {
                    b.Navigation("BenchmarkLinks");

                    b.Navigation("CapabilityLinks");

                    b.Navigation("EnhancementsAsBusinessModelFiles");

                    b.Navigation("EnhancementsAsFlowChartFile");

                    b.Navigation("ImprovementOpportunityLinks");

                    b.Navigation("KpiBenchmarks");

                    b.Navigation("KpiDynamicDataEntryRequestLinks");

                    b.Navigation("KpiResultCapabilityLinks");

                    b.Navigation("OperationProcedureSteps");

                    b.Navigation("OperationProcedures");

                    b.Navigation("Operations");

                    b.Navigation("PartnershipContracts");

                    b.Navigation("PlanSubsubtaskLinks");

                    b.Navigation("RiskManagementProcedureLinks");

                    b.Navigation("StandardLinks");

                    b.Navigation("StandardPresentations");

                    b.Navigation("StandardSubtaskLinks");

                    b.Navigation("StandardTeamFormations");

                    b.Navigation("TagLinks");

                    b.Navigation("Tournaments");

                    b.Navigation("UserRequests");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.LibraryTag", b =>
                {
                    b.Navigation("LibraryFileLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.NationalAgenda", b =>
                {
                    b.Navigation("PartnershipGoalLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Notification", b =>
                {
                    b.Navigation("UserLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Operation", b =>
                {
                    b.Navigation("BenchmarkLinks");

                    b.Navigation("Children");

                    b.Navigation("Enhancements");

                    b.Navigation("Executors");

                    b.Navigation("FormFileLinks");

                    b.Navigation("GoalLinks");

                    b.Navigation("ImprovementOpportunityLinks");

                    b.Navigation("KpiLinks");

                    b.Navigation("PartnerLinks");

                    b.Navigation("PartnershipGoalLinks");

                    b.Navigation("PlanLinks");

                    b.Navigation("PlanTaskLinks");

                    b.Navigation("PolicyLinks");

                    b.Navigation("Procedures");

                    b.Navigation("RiskLinks");

                    b.Navigation("RuleAndRegulationLinks");

                    b.Navigation("SpecificationLinks");

                    b.Navigation("SuccessFactorOperationLinks");

                    b.Navigation("UserRequests");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OperationEnhancement", b =>
                {
                    b.Navigation("FileLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OperationEnhancementType", b =>
                {
                    b.Navigation("OperationEnhancements");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OperationProcedure", b =>
                {
                    b.Navigation("KpiLinks");

                    b.Navigation("Steps");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OperationRuleAndRegulation", b =>
                {
                    b.Navigation("OperationLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OperationSpecification", b =>
                {
                    b.Navigation("OperationSpecificationLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.OrganizationType", b =>
                {
                    b.Navigation("Departments");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnerModel.Partner", b =>
                {
                    b.Navigation("Benchmarks");

                    b.Navigation("OperationExecutors");

                    b.Navigation("OperationLinks");

                    b.Navigation("PartnershipContracts");

                    b.Navigation("PlanLinks");

                    b.Navigation("ServiceLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnerModel.PartnerStandard", b =>
                {
                    b.Navigation("PartnershipContractLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipActivity", b =>
                {
                    b.Navigation("Periods");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipActivityCommunicationTool", b =>
                {
                    b.Navigation("Activities");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipActivityPeriod", b =>
                {
                    b.Navigation("Attachments");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipContract", b =>
                {
                    b.Navigation("Activities");

                    b.Navigation("Goals");

                    b.Navigation("PartnershipFrameworkLinks");

                    b.Navigation("PartnershipPartnerEvaluations");

                    b.Navigation("PartnershipPartnerStandardLinks");

                    b.Navigation("PartnershipScopeLinks");

                    b.Navigation("TerminationRequests");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipField", b =>
                {
                    b.Navigation("PartnershipContracts");

                    b.Navigation("PartnershipScopes");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipFramework", b =>
                {
                    b.Navigation("PartnershipContractLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipGoal", b =>
                {
                    b.Navigation("Activities");

                    b.Navigation("Initiatives");

                    b.Navigation("NationalAgendaLinks");

                    b.Navigation("OperationLinks");

                    b.Navigation("ServiceLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipGoalActivity", b =>
                {
                    b.Navigation("KpiLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipScope", b =>
                {
                    b.Navigation("PartnershipContractLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipType", b =>
                {
                    b.Navigation("PartnershipContracts");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Pillar", b =>
                {
                    b.Navigation("Standards");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Plan", b =>
                {
                    b.Navigation("Dependencies");

                    b.Navigation("InputLinks");

                    b.Navigation("KpiLinks");

                    b.Navigation("MinistryStrategicGoalLinks");

                    b.Navigation("OperationLinks");

                    b.Navigation("PartnerLinks");

                    b.Navigation("PolicyLinks");

                    b.Navigation("Principals");

                    b.Navigation("Resources");

                    b.Navigation("RiskLinks");

                    b.Navigation("StrategicGoalLinks");

                    b.Navigation("Tasks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanCategory", b =>
                {
                    b.Navigation("Plans");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanSubsubtask", b =>
                {
                    b.Navigation("Approvals");

                    b.Navigation("LibraryFileLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanSubtask", b =>
                {
                    b.Navigation("Subsubtasks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.PlanTask", b =>
                {
                    b.Navigation("KpiLinks");

                    b.Navigation("OperationLinks");

                    b.Navigation("Subtasks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Policy", b =>
                {
                    b.Navigation("OperationPolicyLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.RiskModel.Risk", b =>
                {
                    b.Navigation("GoalLinks");

                    b.Navigation("ManagementProcedures");

                    b.Navigation("OperationLinks");

                    b.Navigation("PlanLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.RiskModel.RiskCategory", b =>
                {
                    b.Navigation("Risks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.RiskModel.RiskImpact", b =>
                {
                    b.Navigation("Risks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.RiskModel.RiskManagementProcedure", b =>
                {
                    b.Navigation("LibraryFileLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.RiskModel.RiskManagementStrategy", b =>
                {
                    b.Navigation("Risks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.RiskModel.RiskProbability", b =>
                {
                    b.Navigation("Risks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Service", b =>
                {
                    b.Navigation("CategoryLinks");

                    b.Navigation("DepartmentLinks");

                    b.Navigation("PartnerLinks");

                    b.Navigation("PartnershipGoalLinks");

                    b.Navigation("Providers");

                    b.Navigation("ServiceDeliveryChannelLinks");

                    b.Navigation("ServiceProviderChannelLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.ServiceCategory", b =>
                {
                    b.Navigation("ServiceLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.ServiceDeliveryChannel", b =>
                {
                    b.Navigation("ServiceLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.ServiceProviderChannel", b =>
                {
                    b.Navigation("ServiceLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Standard", b =>
                {
                    b.Navigation("CapabilityLinks");

                    b.Navigation("KpiLinks");

                    b.Navigation("LibraryFileLinks");

                    b.Navigation("Principles");

                    b.Navigation("Tasks");

                    b.Navigation("UserLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StandardSubtask", b =>
                {
                    b.Navigation("Approvals");

                    b.Navigation("Comments");

                    b.Navigation("LibraryFileLinks");

                    b.Navigation("StandardUserLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StandardTask", b =>
                {
                    b.Navigation("Subtasks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StandardUserLink", b =>
                {
                    b.Navigation("StandardSubtaskUserLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StatisticalReport", b =>
                {
                    b.Navigation("Categories");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StatisticalReportCategory", b =>
                {
                    b.Navigation("Children");

                    b.Navigation("DepartmentLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StatisticalReportCategoryDepartmentLink", b =>
                {
                    b.Navigation("Results");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StrategicGoal", b =>
                {
                    b.Navigation("BenchmarkLinks");

                    b.Navigation("KpiLinks");

                    b.Navigation("OperationLinks");

                    b.Navigation("PartnershipGoals");

                    b.Navigation("RiskLinks");

                    b.Navigation("StrategicPerspectiveGoalsLinks");

                    b.Navigation("StrategicPillarGoalsLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.StrategicPlan", b =>
                {
                    b.Navigation("StrategicPerspectives");

                    b.Navigation("StrategicPillars");

                    b.Navigation("StrategicValues");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.SuccessFactor", b =>
                {
                    b.Navigation("OperationLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Team", b =>
                {
                    b.Navigation("Plans");

                    b.Navigation("Subtasks");

                    b.Navigation("Tasks");

                    b.Navigation("UserLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.Tournament", b =>
                {
                    b.Navigation("Pillars");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.User", b =>
                {
                    b.Navigation("Claims");

                    b.Navigation("DepartmentLinks");

                    b.Navigation("NotificationLinks");

                    b.Navigation("PermissionGroupLinks");

                    b.Navigation("PermissionLinks");

                    b.Navigation("Plans");

                    b.Navigation("Subtasks");

                    b.Navigation("Tasks");

                    b.Navigation("TeamLinks");

                    b.Navigation("UserRequestComments");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.App.UserRequest", b =>
                {
                    b.Navigation("RequestComments");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.Permission.Permission", b =>
                {
                    b.Navigation("GroupLinks");

                    b.Navigation("OverriddenPermissions");

                    b.Navigation("Overriders");

                    b.Navigation("UserLinks");
                });

            modelBuilder.Entity("Injaz.Core.Models.DomainClasses.Permission.PermissionGroup", b =>
                {
                    b.Navigation("PermissionLinks");

                    b.Navigation("UserLinks");
                });
#pragma warning restore 612, 618
        }
    }
}
