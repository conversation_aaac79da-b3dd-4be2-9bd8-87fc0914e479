using System;
using System.Linq;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Injaz.Core.Migrations
{
    public partial class ModKpiTypeRmvNullable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Create the appropriate types.
            new[]
                {
                    new { NameAr = "استراتيجي", NameEn = "Strategic", Code = "STR", IsUsedInDashboard = 1, IsUsedForGoalAchievement = 1, IsExcludedFromCalculations = 0 },
                    new { NameAr = "عمليات رئيسي", NameEn = "Operational main", Code = "OPE", IsUsedInDashboard = 0, IsUsedForGoalAchievement = 0, IsExcludedFromCalculations = 0 },
                    new { NameAr = "عمليات دعم", NameEn = "Operation supporting", Code = "SPR", IsUsedInDashboard = 0, IsUsedForGoalAchievement = 0, IsExcludedFromCalculations = 0 },
                    new { NameAr = "عمليات احصائي", NameEn = "Operational statistical", Code = "STA", IsUsedInDashboard = 0, IsUsedForGoalAchievement = 0, IsExcludedFromCalculations = 0 },
                    new { NameAr = "وطني", NameEn = "National", Code = "NAT", IsUsedInDashboard = 1, IsUsedForGoalAchievement = 1, IsExcludedFromCalculations = 0 },
                    new { NameAr = "إقطاعي", NameEn = "Segmental", Code = "SEG", IsUsedInDashboard = 1, IsUsedForGoalAchievement = 1, IsExcludedFromCalculations = 0 },
                }.ToList()
                .ForEach(item =>
                {
                    var query = $"INSERT INTO kpi_types VALUES(" +
                                $"NEWID()," +
                                $"N'{item.NameAr}'," +
                                $"N'{item.NameEn}'," +
                                $"'{item.Code}'," +
                                $"{item.IsUsedForGoalAchievement}," +
                                $"{item.IsUsedInDashboard}," +
                                $"{item.IsExcludedFromCalculations}," +
                                $"GETUTCDATE()," +
                                $"NULL," +
                                $"NULL," +
                                $"NULL," +
                                $"0," +
                                $"NULL" +
                                $")";
                    migrationBuilder.Sql(query);
                });

            // Transfer the data.
            migrationBuilder.Sql(@"
                    UPDATE kpis
                    SET type_id = (SELECT kt.id
                                   FROM kpi_types kt
                                   WHERE SUBSTRING(type, 1, 3) = LOWER(kt.code))
            ");

            migrationBuilder.DropForeignKey(
                name: "FK_kpis_kpi_types_type_id",
                table: "kpis");

            migrationBuilder.AlterColumn<Guid>(
                name: "type_id",
                table: "kpis",
                type: "uniqueidentifier",
                nullable: false,
                defaultValue: new Guid("00000000-0000-0000-0000-000000000000"),
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier",
                oldNullable: true);

            migrationBuilder.AddForeignKey(
                name: "FK_kpis_kpi_types_type_id",
                table: "kpis",
                column: "type_id",
                principalTable: "kpi_types",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_kpis_kpi_types_type_id",
                table: "kpis");

            migrationBuilder.AlterColumn<Guid>(
                name: "type_id",
                table: "kpis",
                type: "uniqueidentifier",
                nullable: true,
                oldClrType: typeof(Guid),
                oldType: "uniqueidentifier");

            migrationBuilder.AddForeignKey(
                name: "FK_kpis_kpi_types_type_id",
                table: "kpis",
                column: "type_id",
                principalTable: "kpi_types",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
