using Microsoft.EntityFrameworkCore.Migrations;

namespace Injaz.Core.Migrations
{
    public partial class RenamePlanExpectedBenefitsProperty : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
                UPDATE plans
                SET expected_benefits = REPLACE(
                    CONVERT(NVARCHAR(MAX), expected_benefits),
                    N'""willAchieve"":',
                    N'""achievementTarget"":'
                )
                WHERE CHARINDEX(N'""willAchieve"":', CONVERT(NVARCHAR(MAX), expected_benefits)) > 0;
            ");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
                UPDATE plans
                SET expected_benefits = REPLACE(
                    CONVERT(NVARCHAR(MAX), expected_benefits),
                    N'""achievementTarget"":',
                    N'""willAchieve"":'
                )
                WHERE CHARINDEX(N'""achievementTarget"":', CONVERT(NVARCHAR(MAX), expected_benefits)) > 0;
            ");
        }
    }
}
