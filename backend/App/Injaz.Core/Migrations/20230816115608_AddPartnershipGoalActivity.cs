using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Injaz.Core.Migrations
{
    public partial class AddPartnershipGoalActivity : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "partnership_goal_activities",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    partnership_goal_id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    name_ar = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: true),
                    name_en = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: true),
                    benefit = table.Column<double>(type: "float", nullable: true),
                    responsibilities = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    end_date = table.Column<DateTime>(type: "datetime2", nullable: true),
                    risks = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    targets = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    target_knowledge = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    creation_time = table.Column<DateTime>(type: "datetime2", nullable: false),
                    created_by_id = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    deletion_time = table.Column<DateTime>(type: "datetime2", nullable: true),
                    deleted_by_id = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    is_deleted = table.Column<int>(type: "int", nullable: false),
                    modification_history = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_partnership_goal_activities", x => x.id);
                    table.ForeignKey(
                        name: "FK_partnership_goal_activities_partnership_goals_partnership_goal_id",
                        column: x => x.partnership_goal_id,
                        principalTable: "partnership_goals",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "partnership_goal_activity_kpi_links",
                columns: table => new
                {
                    partnership_goal_activity_id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    kpi_id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    creation_time = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_partnership_goal_activity_kpi_links", x => new { x.partnership_goal_activity_id, x.kpi_id });
                    table.ForeignKey(
                        name: "FK_partnership_goal_activity_kpi_links_kpis_kpi_id",
                        column: x => x.kpi_id,
                        principalTable: "kpis",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_partnership_goal_activity_kpi_links_partnership_goal_activities_partnership_goal_activity_id",
                        column: x => x.partnership_goal_activity_id,
                        principalTable: "partnership_goal_activities",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_partnership_goal_activities_partnership_goal_id",
                table: "partnership_goal_activities",
                column: "partnership_goal_id");

            migrationBuilder.CreateIndex(
                name: "IX_partnership_goal_activity_kpi_links_kpi_id",
                table: "partnership_goal_activity_kpi_links",
                column: "kpi_id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "partnership_goal_activity_kpi_links");

            migrationBuilder.DropTable(
                name: "partnership_goal_activities");
        }
    }
}
