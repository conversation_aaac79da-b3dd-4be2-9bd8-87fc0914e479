using Microsoft.EntityFrameworkCore.Migrations;

namespace Injaz.Core.Migrations
{
    public partial class MigrateDataFromServiceToServiceClientCategoryLink : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@$"
                -- Step 1: Create and populate the temporary table
                DECLARE @existing_service_client_categories TABLE (
                    id nvarchar(200) NOT NULL,
                    name_ar nvarchar(200) NOT NULL,
                    name_en nvarchar(200) NOT NULL
                );

                -- Step 2: Insert data into the temporary table
                INSERT INTO @existing_service_client_categories (id, name_ar, name_en)
                VALUES
                    ('male', N'رجال', 'Male'),
                    ('resident', N'مقيم', 'Resident'),
                    ('people_of_determination', N'أصحاب الهمم', 'People of determination'),
                    ('female', N'سيدات', 'Female'),
                    ('visitor', N'زائر', 'Visitor'),
                    ('government', N'جهات حكومية', 'Government'),
                    ('private', N'جهات خاصة', 'Private'),
                    ('senior_citizen', N'كبار المواطنين', 'Senior citizens');

                -- Step 3: Create a CTE for new service client categories
                WITH cte_service_client_categories_new AS (
                    SELECT esc.name_ar, esc.name_en
                    FROM services s
                    CROSS APPLY OPENJSON(s.client_categories) j
                    JOIN @existing_service_client_categories esc
                        ON j.value = esc.id COLLATE Arabic_CI_AS
                    GROUP BY esc.name_ar, esc.name_en
                )

                -- Step 4: Insert new service client categories into the service_client_categories table
                INSERT INTO service_client_categories(id, name_ar, name_en, creation_time, is_deleted)
                SELECT NEWID(), name_ar, name_en, GETUTCDATE(), 0
                FROM cte_service_client_categories_new;

                -- Step 5: Create a CTE for existing service client categories
                WITH cte_service_client_categories AS (
                    SELECT s.id AS service_id, scc.id AS client_category_id
                    FROM services s
                    CROSS APPLY OPENJSON(s.client_categories) j
                    JOIN @existing_service_client_categories esc
                        ON j.value = esc.id COLLATE Arabic_CI_AS
                    JOIN service_client_categories scc
                        ON scc.name_en = esc.name_en COLLATE Arabic_CI_AS
                )

                -- Step 6: Insert service-client category links into the service_service_client_category_links table
                INSERT INTO service_service_client_category_links(service_id, service_client_category_id, creation_time)
                SELECT service_id, client_category_id, GETUTCDATE()
                FROM cte_service_client_categories;
            ");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("DELETE FROM service_service_client_category_links");
            migrationBuilder.Sql("DELETE FROM service_client_categories");
        }
    }
}
