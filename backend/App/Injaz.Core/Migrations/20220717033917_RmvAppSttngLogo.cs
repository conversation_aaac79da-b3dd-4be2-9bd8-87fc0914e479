using Microsoft.EntityFrameworkCore.Migrations;

namespace Injaz.Core.Migrations
{
    public partial class RmvAppSttngLogo : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "logo_content_type",
                table: "app_settings");

            migrationBuilder.DropColumn(
                name: "logo_file_name",
                table: "app_settings");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "logo_content_type",
                table: "app_settings",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "logo_file_name",
                table: "app_settings",
                type: "nvarchar(max)",
                nullable: true);
        }
    }
}
