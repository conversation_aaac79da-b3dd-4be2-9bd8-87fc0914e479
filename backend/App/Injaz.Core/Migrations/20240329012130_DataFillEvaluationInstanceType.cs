using Microsoft.EntityFrameworkCore.Migrations;

namespace Injaz.Core.Migrations
{
    public partial class DataFillEvaluationInstanceType : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql(@"
                UPDATE ei
                SET type = IIF(k.id IS NULL, 'risk', 'kpi')
                FROM evaluation_instances ei
                         LEFT JOIN kpis k ON ei.entity_id = k.id
                         LEFT JOIN risks r ON ei.entity_id = r.id
            ");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {

        }
    }
}
