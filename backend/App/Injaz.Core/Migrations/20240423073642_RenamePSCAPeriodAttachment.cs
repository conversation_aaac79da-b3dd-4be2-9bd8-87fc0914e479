using Microsoft.EntityFrameworkCore.Migrations;

namespace Injaz.Core.Migrations
{
    public partial class RenamePSCAPeriodAttachment : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_partnership_activity_period_attachment_partnership_activity_periods_partnership_activity_period_id",
                table: "partnership_activity_period_attachment");

            migrationBuilder.DropPrimaryKey(
                name: "PK_partnership_activity_period_attachment",
                table: "partnership_activity_period_attachment");

            migrationBuilder.RenameTable(
                name: "partnership_activity_period_attachment",
                newName: "partnership_activity_period_attachments");

            migrationBuilder.RenameIndex(
                name: "IX_partnership_activity_period_attachment_partnership_activity_period_id",
                table: "partnership_activity_period_attachments",
                newName: "IX_partnership_activity_period_attachments_partnership_activity_period_id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_partnership_activity_period_attachments",
                table: "partnership_activity_period_attachments",
                column: "id");

            migrationBuilder.AddForeignKey(
                name: "FK_partnership_activity_period_attachments_partnership_activity_periods_partnership_activity_period_id",
                table: "partnership_activity_period_attachments",
                column: "partnership_activity_period_id",
                principalTable: "partnership_activity_periods",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_partnership_activity_period_attachments_partnership_activity_periods_partnership_activity_period_id",
                table: "partnership_activity_period_attachments");

            migrationBuilder.DropPrimaryKey(
                name: "PK_partnership_activity_period_attachments",
                table: "partnership_activity_period_attachments");

            migrationBuilder.RenameTable(
                name: "partnership_activity_period_attachments",
                newName: "partnership_activity_period_attachment");

            migrationBuilder.RenameIndex(
                name: "IX_partnership_activity_period_attachments_partnership_activity_period_id",
                table: "partnership_activity_period_attachment",
                newName: "IX_partnership_activity_period_attachment_partnership_activity_period_id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_partnership_activity_period_attachment",
                table: "partnership_activity_period_attachment",
                column: "id");

            migrationBuilder.AddForeignKey(
                name: "FK_partnership_activity_period_attachment_partnership_activity_periods_partnership_activity_period_id",
                table: "partnership_activity_period_attachment",
                column: "partnership_activity_period_id",
                principalTable: "partnership_activity_periods",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);
        }
    }
}
