using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Injaz.Core.Migrations
{
    public partial class AddUserRequests : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "users_requests",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    content = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    is_closed = table.Column<int>(type: "int", nullable: false),
                    user_id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    operation_id = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    kpi_id = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    library_file_id = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    creation_time = table.Column<DateTime>(type: "datetime2", nullable: false),
                    created_by_id = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    deletion_time = table.Column<DateTime>(type: "datetime2", nullable: true),
                    deleted_by_id = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    is_deleted = table.Column<int>(type: "int", nullable: false),
                    modification_history = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_users_requests", x => x.id);
                    table.ForeignKey(
                        name: "FK_users_requests_kpis_kpi_id",
                        column: x => x.kpi_id,
                        principalTable: "kpis",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_users_requests_library_files_library_file_id",
                        column: x => x.library_file_id,
                        principalTable: "library_files",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_users_requests_operations_operation_id",
                        column: x => x.operation_id,
                        principalTable: "operations",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Restrict);
                    table.ForeignKey(
                        name: "FK_users_requests_users_user_id",
                        column: x => x.user_id,
                        principalTable: "users",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateTable(
                name: "user_request_comments",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    content = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    user_request_id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    user_id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    creation_time = table.Column<DateTime>(type: "datetime2", nullable: false),
                    created_by_id = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    deletion_time = table.Column<DateTime>(type: "datetime2", nullable: true),
                    deleted_by_id = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    is_deleted = table.Column<int>(type: "int", nullable: false),
                    modification_history = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_user_request_comments", x => x.id);
                    table.ForeignKey(
                        name: "FK_user_request_comments_users_requests_user_request_id",
                        column: x => x.user_request_id,
                        principalTable: "users_requests",
                        principalColumn: "id");
                    table.ForeignKey(
                        name: "FK_user_request_comments_users_user_id",
                        column: x => x.user_id,
                        principalTable: "users",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_user_request_comments_user_id",
                table: "user_request_comments",
                column: "user_id");

            migrationBuilder.CreateIndex(
                name: "IX_user_request_comments_user_request_id",
                table: "user_request_comments",
                column: "user_request_id");

            migrationBuilder.CreateIndex(
                name: "IX_users_requests_kpi_id",
                table: "users_requests",
                column: "kpi_id");

            migrationBuilder.CreateIndex(
                name: "IX_users_requests_library_file_id",
                table: "users_requests",
                column: "library_file_id");

            migrationBuilder.CreateIndex(
                name: "IX_users_requests_operation_id",
                table: "users_requests",
                column: "operation_id");

            migrationBuilder.CreateIndex(
                name: "IX_users_requests_user_id",
                table: "users_requests",
                column: "user_id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "user_request_comments");

            migrationBuilder.DropTable(
                name: "users_requests");
        }
    }
}
