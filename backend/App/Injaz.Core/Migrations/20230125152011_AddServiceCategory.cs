using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Injaz.Core.Migrations
{
    public partial class AddServiceCategory : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "service_categories",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    name_ar = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: true),
                    name_en = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: true),
                    creation_time = table.Column<DateTime>(type: "datetime2", nullable: false),
                    created_by_id = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    deletion_time = table.Column<DateTime>(type: "datetime2", nullable: true),
                    deleted_by_id = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    is_deleted = table.Column<int>(type: "int", nullable: false),
                    modification_history = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_service_categories", x => x.id);
                });

            migrationBuilder.CreateTable(
                name: "service_category_links",
                columns: table => new
                {
                    service_Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    department_Id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    creation_time = table.Column<DateTime>(type: "datetime2", nullable: false),
                    id = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_service_category_links", x => new { x.service_Id, x.department_Id });
                    table.ForeignKey(
                        name: "FK_service_category_links_service_categories_department_Id",
                        column: x => x.department_Id,
                        principalTable: "service_categories",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_service_category_links_services_service_Id",
                        column: x => x.service_Id,
                        principalTable: "services",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_service_category_links_department_Id",
                table: "service_category_links",
                column: "department_Id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "service_category_links");

            migrationBuilder.DropTable(
                name: "service_categories");
        }
    }
}
