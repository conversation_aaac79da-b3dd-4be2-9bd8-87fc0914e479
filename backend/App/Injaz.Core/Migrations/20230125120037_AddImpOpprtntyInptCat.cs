using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Injaz.Core.Migrations
{
    public partial class AddImpOpprtntyInptCat : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "inputs_category_id",
                table: "improvement_opportunities",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateTable(
                name: "improvement_opportunity_inputs_categories",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    name_ar = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: true),
                    name_en = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: true),
                    creation_time = table.Column<DateTime>(type: "datetime2", nullable: false),
                    created_by_id = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    deletion_time = table.Column<DateTime>(type: "datetime2", nullable: true),
                    deleted_by_id = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    is_deleted = table.Column<int>(type: "int", nullable: false),
                    modification_history = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_improvement_opportunity_inputs_categories", x => x.id);
                });

            migrationBuilder.CreateIndex(
                name: "IX_improvement_opportunities_inputs_category_id",
                table: "improvement_opportunities",
                column: "inputs_category_id");

            migrationBuilder.AddForeignKey(
                name: "FK_improvement_opportunities_improvement_opportunity_inputs_categories_inputs_category_id",
                table: "improvement_opportunities",
                column: "inputs_category_id",
                principalTable: "improvement_opportunity_inputs_categories",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_improvement_opportunities_improvement_opportunity_inputs_categories_inputs_category_id",
                table: "improvement_opportunities");

            migrationBuilder.DropTable(
                name: "improvement_opportunity_inputs_categories");

            migrationBuilder.DropIndex(
                name: "IX_improvement_opportunities_inputs_category_id",
                table: "improvement_opportunities");

            migrationBuilder.DropColumn(
                name: "inputs_category_id",
                table: "improvement_opportunities");
        }
    }
}
