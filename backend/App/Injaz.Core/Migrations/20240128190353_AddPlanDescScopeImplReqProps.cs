using Microsoft.EntityFrameworkCore.Migrations;

namespace Injaz.Core.Migrations
{
    public partial class AddPlanDescScopeImplReqProps : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "description",
                table: "plans",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "implementation_requirement",
                table: "plans",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "work_scope",
                table: "plans",
                type: "nvarchar(1024)",
                maxLength: 1024,
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "description",
                table: "plans");

            migrationBuilder.DropColumn(
                name: "implementation_requirement",
                table: "plans");

            migrationBuilder.DropColumn(
                name: "work_scope",
                table: "plans");
        }
    }
}
