using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Injaz.Core.Migrations
{
    public partial class AddRdTm : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "is_read",
                table: "notification_user_links");

            migrationBuilder.AddColumn<DateTime>(
                name: "read_time",
                table: "notification_user_links",
                type: "datetime2",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "read_time",
                table: "notification_user_links");

            migrationBuilder.AddColumn<int>(
                name: "is_read",
                table: "notification_user_links",
                type: "int",
                nullable: false,
                defaultValue: 0);
        }
    }
}
