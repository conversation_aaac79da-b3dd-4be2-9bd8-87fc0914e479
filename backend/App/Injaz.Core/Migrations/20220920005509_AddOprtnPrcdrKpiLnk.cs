using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Injaz.Core.Migrations
{
    public partial class AddOprtnPrcdrKpiLnk : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "operation_procedure_kpi_links",
                columns: table => new
                {
                    procedure_id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    kpi_id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    creation_time = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_operation_procedure_kpi_links", x => new { x.procedure_id, x.kpi_id });
                    table.ForeignKey(
                        name: "FK_operation_procedure_kpi_links_kpis_kpi_id",
                        column: x => x.kpi_id,
                        principalTable: "kpis",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_operation_procedure_kpi_links_operation_procedures_procedure_id",
                        column: x => x.procedure_id,
                        principalTable: "operation_procedures",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_operation_procedure_kpi_links_kpi_id",
                table: "operation_procedure_kpi_links",
                column: "kpi_id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "operation_procedure_kpi_links");
        }
    }
}
