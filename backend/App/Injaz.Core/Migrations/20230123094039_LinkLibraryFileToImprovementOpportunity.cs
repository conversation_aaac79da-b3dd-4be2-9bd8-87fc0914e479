using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Injaz.Core.Migrations
{
    public partial class LinkLibraryFileToImprovementOpportunity : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "improvement_opportunity_library_file_links",
                columns: table => new
                {
                    improvement_opportunity_id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    library_file_id = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_improvement_opportunity_library_file_links", x => new { x.improvement_opportunity_id, x.library_file_id });
                    table.ForeignKey(
                        name: "FK_improvement_opportunity_library_file_links_improvement_opportunities_improvement_opportunity_id",
                        column: x => x.improvement_opportunity_id,
                        principalTable: "improvement_opportunities",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_improvement_opportunity_library_file_links_library_files_library_file_id",
                        column: x => x.library_file_id,
                        principalTable: "library_files",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_improvement_opportunity_library_file_links_library_file_id",
                table: "improvement_opportunity_library_file_links",
                column: "library_file_id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "improvement_opportunity_library_file_links");
        }
    }
}
