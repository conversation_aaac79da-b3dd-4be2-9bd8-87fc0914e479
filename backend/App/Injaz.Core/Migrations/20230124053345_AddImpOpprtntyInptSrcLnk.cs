using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Injaz.Core.Migrations
{
    public partial class AddImpOpprtntyInptSrcLnk : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "improvement_opportunity_input_source_links",
                columns: table => new
                {
                    opportunity_id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    source_id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    creation_time = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_improvement_opportunity_input_source_links", x => new { x.opportunity_id, x.source_id });
                    table.ForeignKey(
                        name: "FK_improvement_opportunity_input_source_links_improvement_opportunities_opportunity_id",
                        column: x => x.opportunity_id,
                        principalTable: "improvement_opportunities",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_improvement_opportunity_input_source_links_improvement_opportunity_input_sources_source_id",
                        column: x => x.source_id,
                        principalTable: "improvement_opportunity_input_sources",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_improvement_opportunity_input_source_links_source_id",
                table: "improvement_opportunity_input_source_links",
                column: "source_id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "improvement_opportunity_input_source_links");
        }
    }
}
