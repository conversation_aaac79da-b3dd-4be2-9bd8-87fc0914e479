using Microsoft.EntityFrameworkCore.Migrations;

namespace Injaz.Core.Migrations
{
    public partial class RenamePartnershipPartnerStandardLinkCol : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_partnership_contract_partner_standard_links_partner_standards_partnership_partner_standard_id",
                table: "partnership_contract_partner_standard_links");

            migrationBuilder.RenameColumn(
                name: "partnership_partner_standard_id",
                table: "partnership_contract_partner_standard_links",
                newName: "partner_standard_id");

            migrationBuilder.RenameIndex(
                name: "IX_partnership_contract_partner_standard_links_partnership_partner_standard_id",
                table: "partnership_contract_partner_standard_links",
                newName: "IX_partnership_contract_partner_standard_links_partner_standard_id");

            migrationBuilder.AddForeignKey(
                name: "FK_partnership_contract_partner_standard_links_partner_standards_partner_standard_id",
                table: "partnership_contract_partner_standard_links",
                column: "partner_standard_id",
                principalTable: "partner_standards",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_partnership_contract_partner_standard_links_partner_standards_partner_standard_id",
                table: "partnership_contract_partner_standard_links");

            migrationBuilder.RenameColumn(
                name: "partner_standard_id",
                table: "partnership_contract_partner_standard_links",
                newName: "partnership_partner_standard_id");

            migrationBuilder.RenameIndex(
                name: "IX_partnership_contract_partner_standard_links_partner_standard_id",
                table: "partnership_contract_partner_standard_links",
                newName: "IX_partnership_contract_partner_standard_links_partnership_partner_standard_id");

            migrationBuilder.AddForeignKey(
                name: "FK_partnership_contract_partner_standard_links_partner_standards_partnership_partner_standard_id",
                table: "partnership_contract_partner_standard_links",
                column: "partnership_partner_standard_id",
                principalTable: "partner_standards",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
