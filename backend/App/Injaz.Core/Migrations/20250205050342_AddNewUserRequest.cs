using System;
using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Injaz.Core.Migrations
{
    public partial class AddNewUserRequest : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "new_user_requests",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    name_ar = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: true),
                    name_en = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: true),
                    email = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: true),
                    gender = table.Column<string>(type: "nvarchar(16)", maxLength: 16, nullable: true),
                    employee_number = table.Column<string>(type: "nvarchar(32)", maxLength: 32, nullable: true),
                    rank = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true),
                    requested_permissions = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    flow_state = table.Column<string>(type: "nvarchar(max)", nullable: true),
                    creation_time = table.Column<DateTime>(type: "datetime2", nullable: false),
                    created_by_id = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    deletion_time = table.Column<DateTime>(type: "datetime2", nullable: true),
                    deleted_by_id = table.Column<Guid>(type: "uniqueidentifier", nullable: true),
                    is_deleted = table.Column<int>(type: "int", nullable: false),
                    modification_history = table.Column<string>(type: "nvarchar(max)", nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_new_user_requests", x => x.id);
                });
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "new_user_requests");
        }
    }
}
