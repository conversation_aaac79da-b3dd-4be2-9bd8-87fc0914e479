using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Injaz.Core.Migrations
{
    public partial class AddImprovementOpportunityPrincipleLink : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "improvement_opportunity_principle_links",
                columns: table => new
                {
                    improvement_opportunity_id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    principle_id = table.Column<Guid>(type: "uniqueidentifier", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_improvement_opportunity_principle_links", x => new { x.improvement_opportunity_id, x.principle_id });
                    table.ForeignKey(
                        name: "FK_improvement_opportunity_principle_links_improvement_opportunities_improvement_opportunity_id",
                        column: x => x.improvement_opportunity_id,
                        principalTable: "improvement_opportunities",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_improvement_opportunity_principle_links_principles_principle_id",
                        column: x => x.principle_id,
                        principalTable: "principles",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_improvement_opportunity_principle_links_principle_id",
                table: "improvement_opportunity_principle_links",
                column: "principle_id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "improvement_opportunity_principle_links");
        }
    }
}
