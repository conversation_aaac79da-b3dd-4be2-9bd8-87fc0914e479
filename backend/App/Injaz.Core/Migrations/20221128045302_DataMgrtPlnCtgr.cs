using System.Linq;
using Microsoft.EntityFrameworkCore.Migrations;
using System;

namespace Injaz.Core.Migrations
{
    public partial class DataMgrtPlnCtgr : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            // Create categories
            new []
            {
                new {NameAr = "ادارة", NameEn = "Department"},
                new {NameAr = "عملية", NameEn = "Operation"},
                new {NameAr = "مهمة", NameEn = "Task"},
                new {NameAr = "نظام", NameEn = "System"},
                new {NameAr = "آخر", NameEn = "Other"},
            }.ToList()
                .ForEach(item =>
                {
                    var query = $"INSERT INTO plan_categories VALUES(" +
                                $"NEWID()," +
                                $"N'{item.NameAr}'," +
                                $"N'{item.NameEn}'," +
                                $"'{DateTime.UtcNow.ToString("u")}'," +
                                $"NULL," +
                                $"NULL," +
                                $"NULL," +
                                $"0," +
                                $"NULL" +
                                $")";
                    migrationBuilder.Sql(query);
                });
            
            // Transfer the data.
            migrationBuilder.Sql(@"
                UPDATE plans
                SET category = 'department'
                WHERE category = 'department_operational_plan'
            ");
            
            migrationBuilder.Sql(@"
                    UPDATE plans
                    SET category_id = (SELECT pc.id
                                   FROM plan_categories pc
                                   WHERE category = LOWER(pc.name_en))
            ");

        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {

        }
    }
}
