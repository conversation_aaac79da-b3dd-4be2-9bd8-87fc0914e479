using Microsoft.EntityFrameworkCore.Migrations;

namespace Injaz.Core.Migrations
{
    public partial class RenamePartnershipStandardLinksTable : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_partnership_contract_partner_standard_links_partner_standards_partner_standard_id",
                table: "partnership_contract_partner_standard_links");

            migrationBuilder.DropForeignKey(
                name: "FK_partnership_contract_partner_standard_links_partnership_contracts_partnership_contract_id",
                table: "partnership_contract_partner_standard_links");

            migrationBuilder.DropPrimaryKey(
                name: "PK_partnership_contract_partner_standard_links",
                table: "partnership_contract_partner_standard_links");

            migrationBuilder.RenameTable(
                name: "partnership_contract_partner_standard_links",
                newName: "partnership_partner_standard_links");

            migrationBuilder.RenameIndex(
                name: "IX_partnership_contract_partner_standard_links_partner_standard_id",
                table: "partnership_partner_standard_links",
                newName: "IX_partnership_partner_standard_links_partner_standard_id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_partnership_partner_standard_links",
                table: "partnership_partner_standard_links",
                columns: new[] { "partnership_contract_id", "partner_standard_id" });

            migrationBuilder.AddForeignKey(
                name: "FK_partnership_partner_standard_links_partner_standards_partner_standard_id",
                table: "partnership_partner_standard_links",
                column: "partner_standard_id",
                principalTable: "partner_standards",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_partnership_partner_standard_links_partnership_contracts_partnership_contract_id",
                table: "partnership_partner_standard_links",
                column: "partnership_contract_id",
                principalTable: "partnership_contracts",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_partnership_partner_standard_links_partner_standards_partner_standard_id",
                table: "partnership_partner_standard_links");

            migrationBuilder.DropForeignKey(
                name: "FK_partnership_partner_standard_links_partnership_contracts_partnership_contract_id",
                table: "partnership_partner_standard_links");

            migrationBuilder.DropPrimaryKey(
                name: "PK_partnership_partner_standard_links",
                table: "partnership_partner_standard_links");

            migrationBuilder.RenameTable(
                name: "partnership_partner_standard_links",
                newName: "partnership_contract_partner_standard_links");

            migrationBuilder.RenameIndex(
                name: "IX_partnership_partner_standard_links_partner_standard_id",
                table: "partnership_contract_partner_standard_links",
                newName: "IX_partnership_contract_partner_standard_links_partner_standard_id");

            migrationBuilder.AddPrimaryKey(
                name: "PK_partnership_contract_partner_standard_links",
                table: "partnership_contract_partner_standard_links",
                columns: new[] { "partnership_contract_id", "partner_standard_id" });

            migrationBuilder.AddForeignKey(
                name: "FK_partnership_contract_partner_standard_links_partner_standards_partner_standard_id",
                table: "partnership_contract_partner_standard_links",
                column: "partner_standard_id",
                principalTable: "partner_standards",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);

            migrationBuilder.AddForeignKey(
                name: "FK_partnership_contract_partner_standard_links_partnership_contracts_partnership_contract_id",
                table: "partnership_contract_partner_standard_links",
                column: "partnership_contract_id",
                principalTable: "partnership_contracts",
                principalColumn: "id",
                onDelete: ReferentialAction.Cascade);
        }
    }
}
