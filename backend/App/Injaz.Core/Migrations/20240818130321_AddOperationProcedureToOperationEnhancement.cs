using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Injaz.Core.Migrations
{
    public partial class AddOperationProcedureToOperationEnhancement : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<Guid>(
                name: "procedure_id",
                table: "operation_enhancements",
                type: "uniqueidentifier",
                nullable: true);

            migrationBuilder.CreateIndex(
                name: "IX_operation_enhancements_procedure_id",
                table: "operation_enhancements",
                column: "procedure_id");

            migrationBuilder.AddForeignKey(
                name: "FK_operation_enhancements_operation_procedures_procedure_id",
                table: "operation_enhancements",
                column: "procedure_id",
                principalTable: "operation_procedures",
                principalColumn: "id",
                onDelete: ReferentialAction.Restrict);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropForeignKey(
                name: "FK_operation_enhancements_operation_procedures_procedure_id",
                table: "operation_enhancements");

            migrationBuilder.DropIndex(
                name: "IX_operation_enhancements_procedure_id",
                table: "operation_enhancements");

            migrationBuilder.DropColumn(
                name: "procedure_id",
                table: "operation_enhancements");
        }
    }
}
