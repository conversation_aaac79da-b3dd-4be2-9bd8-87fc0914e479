using Microsoft.EntityFrameworkCore.Migrations;

#nullable disable

namespace Injaz.Core.Migrations
{
    public partial class DropDepartmentEntryRatioFunction : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.Sql("DROP FUNCTION IF EXISTS fn_obtain_department_entry_ratio");

        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
                migrationBuilder.Sql(@"

                 CREATE FUNCTION fn_obtain_department_entry_ratio
                 (
                    @department_id UNIQUEIDENTIFIER,
                    @year INT,
                    @at_least_result INT = 0,
                    @at_least_on_attachment INT = 0,
                    @at_least_on_capability INT = 0
                 )
                    RETURNS FLOAT
                 AS
                BEGIN
                    DECLARE @current_year INT = DATEPART(YYYY, GETUTCDATE());
                    DECLARE @current_month INT = DATEPART(MM, GETUTCDATE());
                    DECLARE @one_third FLOAT = 0.3333333333333;

                    RETURN (SELECT AVG(score)
                            FROM (SELECT IIF(SUM(filled) = COUNT(*), 1, 0) * IIF(@at_least_result = 0, @one_third, 1) +
                                         CASE
                                             -- If at least result required, then do not
                                             -- include the attachment and capability into
                                             -- the equation.
                                             WHEN @at_least_result = 0 THEN
                                                         IIF(
                                                             -- Instead of counting attachments/capabilities for every period,
                                                             -- ensure that at least one item happened for each period
                                                                     SUM(IIF(attachment_count = 0, 0, 1)) >=
                                                                     IIF(@at_least_on_attachment = 1, 1, COUNT(*)),
                                                                     1,
                                                                     0)
                                                         * @one_third +
                                                         IIF(
                                                                     SUM(IIF(capability_count = 0, 0, 1)) >=
                                                                     IIF(@at_least_on_capability = 1, 1, COUNT(*)),
                                                                     1,
                                                                     0) *
                                                         @one_third
                                             ELSE 0
                                             END
                                             AS score
                                  FROM (SELECT t1.result_id                 AS result_id,
                                               t1.input_mode_a              AS input_mode_a,
                                               t1.input_mode_b              AS input_mode_b,
                                               t1.formula                   AS formula,
                                               t1.period_id                 AS period_id,
                                               t1.period                    AS period,
                                               t1.capability_count          AS capability_count,

                                               -- If the id of the attachment is null, then it means
                                               -- the outer join could not find an attachment for the
                                               -- id result and period.
                                               SUM(IIF(a.id IS NULL, 0, 1)) AS attachment_count,
                                               CASE
                                                   WHEN (input_mode_a = 'auto' OR a IS NOT NULL) AND
                                                        (input_mode_b = 'auto' OR b IS NOT NULL OR NOT (formula LIKE N'%B%')) THEN 1
                                                   ELSE 0 END               AS filled
                                        FROM (SELECT r.id                         AS result_id,
                                                     r.input_mode_a               AS input_mode_a,
                                                     r.input_mode_b               AS input_mode_b,
                                                     r.formula                    AS formula,
                                                     p.id                         AS period_id,
                                                     p.period                     AS period,
                                                     p.a                          AS a,
                                                     p.b                          AS b,

                                                     -- If the id of the capability is null, then it means
                                                     -- the outer join could not find an capability for the
                                                     -- period id.
                                                     SUM(IIF(c.id IS NULL, 0, 1)) AS capability_count
                                              FROM kpi_results r
                                                       INNER JOIN kpis k ON r.kpi_id = k.id AND k.status = 'active' AND k.is_deleted = 0
                                                       INNER JOIN departments cd ON cd.id = @department_id
                                                       INNER JOIN departments d ON d.id = r.department_id AND d.is_deleted = 0
                                                       INNER JOIN kpi_result_periods p ON r.id = p.kpi_result_id
                                                       LEFT OUTER JOIN kpi_result_capabilities c ON p.id = c.kpi_result_period_id
                                              WHERE r.year = @year
                                                AND r.is_deleted = 0
                                                AND LEFT(d.hierarchy_code, LEN(cd.hierarchy_code)) = cd.hierarchy_code

                                                -- Ensure period already happened.
                                                AND p.period < (
                                                  CASE
                                                      WHEN @current_year < r.year THEN 1
                                                      WHEN @current_year > r.year THEN 12
                                                      ELSE
                                                              (CASE
                                                                   WHEN r.measurement_cycle = 'annual' THEN 1
                                                                   WHEN r.measurement_cycle = 'semi_annual'
                                                                       THEN IIF(@current_month < 7, 1, 2)
                                                                   WHEN r.measurement_cycle = 'quarter' THEN
                                                                       (
                                                                           CASE
                                                                               WHEN @current_month < 4 THEN 1
                                                                               WHEN @current_month < 7 THEN 2
                                                                               WHEN @current_month < 10 THEN 3
                                                                               ELSE 4
                                                                               END
                                                                           )
                                                                   WHEN r.measurement_cycle = 'month' THEN @current_month
                                                                   ELSE -1
                                                                  END) - 1
                                                      END
                                                  )

                                                -- Take all the columns you need for the upper level queries.
                                              GROUP BY p.id, p.period, p.a, p.b, r.id, r.input_mode_a, r.input_mode_b, r.formula) t1
                                                 LEFT OUTER JOIN kpi_result_attachment a
                                                                 ON a.kpi_result_id = t1.result_id AND a.period = t1.period

                                             -- Take all the columns you need for the upper level queries.
                                        GROUP BY t1.period_id, t1.period, t1.a, t1.b, t1.capability_count, t1.result_id,
                                                 t1.input_mode_a, t1.input_mode_b,
                                                 t1.formula) t2
                                  GROUP BY t2.result_id
                                 ) t3)
                END
            ");
        }
    }
}
