using Microsoft.EntityFrameworkCore.Migrations;

namespace Injaz.Core.Migrations
{
    public partial class AddNewFieldsToPlanSubTask : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.AddColumn<string>(
                name: "alternative",
                table: "plan_subtasks",
                type: "nvarchar(max)",
                nullable: true);

            migrationBuilder.AddColumn<string>(
                name: "not_performing_reason",
                table: "plan_subtasks",
                type: "nvarchar(max)",
                nullable: true);
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropColumn(
                name: "alternative",
                table: "plan_subtasks");

            migrationBuilder.DropColumn(
                name: "not_performing_reason",
                table: "plan_subtasks");
        }
    }
}
