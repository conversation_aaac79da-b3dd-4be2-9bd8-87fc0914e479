using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Injaz.Core.Migrations
{
    public partial class LinkPlanWithMinistryStrategicGoal : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "plan_ministry_strategic_goal_links",
                columns: table => new
                {
                    plan_id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    ministry_strategic_goal_id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    creation_time = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_plan_ministry_strategic_goal_links", x => new { x.plan_id, x.ministry_strategic_goal_id });
                    table.ForeignKey(
                        name: "FK_plan_ministry_strategic_goal_links_ministry_strategic_goals_ministry_strategic_goal_id",
                        column: x => x.ministry_strategic_goal_id,
                        principalTable: "ministry_strategic_goals",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                    table.ForeignKey(
                        name: "FK_plan_ministry_strategic_goal_links_plans_plan_id",
                        column: x => x.plan_id,
                        principalTable: "plans",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_plan_ministry_strategic_goal_links_ministry_strategic_goal_id",
                table: "plan_ministry_strategic_goal_links",
                column: "ministry_strategic_goal_id");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "plan_ministry_strategic_goal_links");
        }
    }
}
