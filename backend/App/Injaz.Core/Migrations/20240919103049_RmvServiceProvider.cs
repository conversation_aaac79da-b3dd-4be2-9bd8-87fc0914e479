using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Injaz.Core.Migrations
{
    public partial class RmvServiceProvider : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "service_providers");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "service_providers",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    creation_time = table.Column<DateTime>(type: "datetime2", nullable: false),
                    name = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true),
                    position = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true),
                    service_id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    site = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true),
                    training_hours = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true),
                    training_type = table.Column<string>(type: "nvarchar(256)", maxLength: 256, nullable: true)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_service_providers", x => x.id);
                    table.ForeignKey(
                        name: "FK_service_providers_services_service_id",
                        column: x => x.service_id,
                        principalTable: "services",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_service_providers_service_id",
                table: "service_providers",
                column: "service_id");
        }
    }
}
