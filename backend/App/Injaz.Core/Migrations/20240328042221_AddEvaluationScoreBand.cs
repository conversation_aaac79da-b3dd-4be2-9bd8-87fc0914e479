using System;
using Microsoft.EntityFrameworkCore.Migrations;

namespace Injaz.Core.Migrations
{
    public partial class AddEvaluationScoreBand : Migration
    {
        protected override void Up(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.CreateTable(
                name: "evaluation_score_bands",
                columns: table => new
                {
                    id = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    name_ar = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: true),
                    name_en = table.Column<string>(type: "nvarchar(1024)", maxLength: 1024, nullable: true),
                    from = table.Column<double>(type: "float", nullable: true),
                    to = table.Column<double>(type: "float", nullable: true),
                    color = table.Column<string>(type: "nvarchar(16)", maxLength: 16, nullable: true),
                    EvaluationId = table.Column<Guid>(type: "uniqueidentifier", nullable: false),
                    creation_time = table.Column<DateTime>(type: "datetime2", nullable: false)
                },
                constraints: table =>
                {
                    table.PrimaryKey("PK_evaluation_score_bands", x => x.id);
                    table.ForeignKey(
                        name: "FK_evaluation_score_bands_evaluations_EvaluationId",
                        column: x => x.EvaluationId,
                        principalTable: "evaluations",
                        principalColumn: "id",
                        onDelete: ReferentialAction.Cascade);
                });

            migrationBuilder.CreateIndex(
                name: "IX_evaluation_score_bands_EvaluationId",
                table: "evaluation_score_bands",
                column: "EvaluationId");
        }

        protected override void Down(MigrationBuilder migrationBuilder)
        {
            migrationBuilder.DropTable(
                name: "evaluation_score_bands");
        }
    }
}
