using System.IO;
using System.Threading.Tasks;

namespace Injaz.Core.Services.FileManager;

public class FileManagerResult
{
    public bool Succeeded { get; set; }
    public string ErrorMessage { get; set; }
}

public interface IFileManager
{
    public Task<FileManagerResult> PutAsync(Stream stream, string dir, string name, string contentType = null);

    public Task<FileManagerResult> DeleteAsync(string path);

    public Task<Stream> GetAsync(string path);

    public Task<Stream> GetAsync(string path, long start, long end);

    public Task<long> GetLengthAsync(string path);
}
