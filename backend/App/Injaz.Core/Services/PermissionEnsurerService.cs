using System;
using System.Threading.Tasks;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Authorization.Policy;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Options;

namespace Injaz.Core.Services;

public class PermissionEnsurerService
{
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IPolicyEvaluator _policyEvaluator;
    private readonly IOptions<AuthorizationOptions> _authorizationOptions;

    public PermissionEnsurerService(
        IHttpContextAccessor httpContextAccessor,
        IPolicyEvaluator policyEvaluator,
        IOptions<AuthorizationOptions> authorizationOptions)
    {
        _httpContextAccessor = httpContextAccessor;
        _policyEvaluator = policyEvaluator;
        _authorizationOptions = authorizationOptions;
    }

    public async Task<bool> Ensure(Guid userId, string permissionName)
    {
        return await Ensure(permissionName);
    }

    public async Task<bool> Ensure(string permissionName)
    {
        if (_httpContextAccessor.HttpContext == null)
        {
            throw new Exception("Cannot run `Ensure` outside of an http context.");
        }

        var policy = _authorizationOptions.Value.GetPolicy(permissionName);

        if (policy == null) return false;

        var authenticationResult = await _policyEvaluator.AuthenticateAsync(policy, _httpContextAccessor.HttpContext);
        var result =
            await _policyEvaluator.AuthorizeAsync(policy, authenticationResult, _httpContextAccessor.HttpContext, null);

        return result.Succeeded;
    }
}
