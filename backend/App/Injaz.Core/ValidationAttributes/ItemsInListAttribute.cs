using System.Collections;
using System.ComponentModel.DataAnnotations;
using System.Linq;
using Microsoft.AspNetCore.Mvc.DataAnnotations;
using Microsoft.AspNetCore.Mvc.ModelBinding.Validation;
using Microsoft.Extensions.Localization;

namespace Injaz.Core.ValidationAttributes;

public class ItemsInListAttribute : ValidationAttribute
{
    private readonly object[] _list;

    public ItemsInListAttribute(params object[] list)
    {
        _list = list;
    }

    protected override ValidationResult IsValid(object value, ValidationContext validationContext)
    {
        if (value == null) return ValidationResult.Success;

        if (!(value is IEnumerable items))
        {
            return new ValidationResult(ErrorMessage);
        }

        var enumerator = items.GetEnumerator();
        while (enumerator.MoveNext())
        {
            if (!_list.Contains(enumerator.Current))
            {
                return new ValidationResult(ErrorMessage);
            }
        }

        return ValidationResult.Success;
    }
}

public class ItemsInListAttributeAdapter : AttributeAdapterBase<ItemsInListAttribute>
{
    public ItemsInListAttributeAdapter(ItemsInListAttribute attribute, IStringLocalizer stringLocalizer)
        : base(attribute, stringLocalizer)
    {
    }

    public override void AddValidation(ClientModelValidationContext context)
    {
    }

    public override string GetErrorMessage(ModelValidationContextBase validationContext)
    {
        return GetErrorMessage(validationContext.ModelMetadata, validationContext.ModelMetadata.GetDisplayName());
    }
}
