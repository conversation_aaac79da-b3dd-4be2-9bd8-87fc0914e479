using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Linq;
using System.Linq.Expressions;
using System.Threading.Tasks;
using LinqKit;
using Injaz.Core.Dtos.User;
using Injaz.Core.Flow.Interfaces;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Models.SqlFunctions;
using Injaz.Core.Permission;

namespace Injaz.Core.Flow.Services;

public abstract class DefaultFlowService<T> : FlowService<T> where T : class, IDefaultFlowEntity
{
    private readonly IFlowDbContext _db;

    protected DefaultFlowService(IFlowDbContext db) : base(db)
    {
        _db = db;
    }

    public override Dictionary<string, string[]> GetPaths()
    {
        return new()
        {
            { DefaultFlowState.Draft, new[] { DefaultFlowState.Submitted } },
            {
                DefaultFlowState.Submitted,
                new[]
                {
                    DefaultFlowState.Approved, DefaultFlowState.ApprovedImmediate, DefaultFlowState.ApprovedFinal,
                    DefaultFlowState.Rejected, DefaultFlowState.RejectedFinal
                }
            },
            {
                DefaultFlowState.Approved,
                new[]
                {
                    DefaultFlowState.Approved, DefaultFlowState.ApprovedImmediate, DefaultFlowState.ApprovedFinal,
                    DefaultFlowState.Rejected, DefaultFlowState.RejectedFinal
                }
            },
            {
                DefaultFlowState.ApprovedImmediate,
                new[] { DefaultFlowState.ApprovedFinal, DefaultFlowState.RejectedFinal }
            },
            { DefaultFlowState.Rejected, new[] { DefaultFlowState.Submitted } },
            { DefaultFlowState.RejectedFinal, new[] { DefaultFlowState.Submitted } },
            { DefaultFlowState.ApprovedFinal, CanBeReset() ? new[] { DefaultFlowState.Draft } : Array.Empty<string>() }
        };
    }

    public Expression<Func<T, IQueryable<Department>>> NextApprovingDepartment()
    {
        var departmentExp = GetEntityDepartment();

        var approvalCount = GetApprovalCount();
        var maxLevel = GetMaxLevel() * Department.HierarchyCodeLength;
        var rejectionResetsCycle =
            DoesRejectionResetsCycle() ? DefaultFlowState.Rejected : DefaultFlowState.ApprovedFinal;

        var permissions = GetPermissions();

        return entity => ObtainNextApprovingDepartmentSqlFunction.Call(
            entity.Id,
            departmentExp.Invoke(entity) == null ? null : departmentExp.Invoke(entity).HierarchyCode,
            approvalCount,
            permissions.Intermediate,
            maxLevel,
            rejectionResetsCycle
        );
    }

    protected abstract DefaultFlowPermissions GetPermissions();

    protected virtual int GetApprovalCount()
    {
        return 1;
    }

    protected virtual int GetMaxLevel()
    {
        return 0;
    }

    protected virtual bool DoesRejectionResetsCycle()
    {
        return true;
    }

    protected virtual bool CanBeReset()
    {
        return true;
    }

    protected virtual Task NotifyForApproval(T entity, UserSimpleDto[] users)
    {
        return Task.CompletedTask;
    }

    protected virtual Task NotifyForImmediateApproval(T entity, UserSimpleDto[] users)
    {
        return Task.CompletedTask;
    }

    protected virtual Task NotifyForFinalApproval(T entity, UserSimpleDto[] users)
    {
        return Task.CompletedTask;
    }

    protected virtual Task NotifyFinalApprovalIsDone(T entity, UserSimpleDto[] users)
    {
        return Task.CompletedTask;
    }

    protected virtual Task NotifyForRejection(T entity, UserSimpleDto[] users)
    {
        return Task.CompletedTask;
    }

    protected abstract Expression<Func<T, Department>> GetEntityDepartment();

    protected override Expression<Func<T, bool>> CanMoveTo(string nextState)
    {
        var nextApprovingDepartmentExp = NextApprovingDepartment();
        Expression<Func<T, bool>> hasNextApprovingDepartmentExp =
            entity => nextApprovingDepartmentExp.Invoke(entity).Any();


        switch (nextState)
        {
            case DefaultFlowState.Submitted:
            case DefaultFlowState.Draft:
                return null;

            case DefaultFlowState.Approved:
            case DefaultFlowState.ApprovedImmediate:
            case DefaultFlowState.Rejected:
                return entity => hasNextApprovingDepartmentExp.Invoke(entity);

            case DefaultFlowState.ApprovedFinal:
            case DefaultFlowState.RejectedFinal:
                return entity => !hasNextApprovingDepartmentExp.Invoke(entity);

            default:
                throw new InvalidEnumArgumentException("Invalid approval flow state.");
        }
    }

    protected override Expression<Func<T, Guid, bool>> CanDepartmentMove(string nextState)
    {
        var nextApprovingDepartmentExp = NextApprovingDepartment();
        var departmentExpression = GetEntityDepartment();

        switch (nextState)
        {
            case DefaultFlowState.Draft:
            case DefaultFlowState.ApprovedFinal:
            case DefaultFlowState.RejectedFinal:
                return null;
            case DefaultFlowState.Submitted:
                return (entity, departmentId) =>
                    departmentExpression.Invoke(entity) == null ||
                    departmentExpression.Invoke(entity).Id == departmentId;
            case DefaultFlowState.Approved:
            case DefaultFlowState.ApprovedImmediate:
            case DefaultFlowState.Rejected:
                return (entity, departmentId) =>
                    nextApprovingDepartmentExp.Invoke(entity).Any(x => x.Id == departmentId);
            default:
                throw new InvalidEnumArgumentException("Invalid approval flow state.");
        }
    }

    protected override Expression<Func<T, Guid, bool>> CanUserMove(string nextState)
    {
        var permissions = GetPermissions();

        var departmentExpression = GetEntityDepartment();

        return nextState switch
        {
            DefaultFlowState.Draft => (entity, userId) =>
                EnsureUserHasPermissionSqlFunction.Call(permissions.Approve, userId),
            DefaultFlowState.Submitted => (entity, userId) =>
                EnsureUserHasPermissionSqlFunction.Call(PermissionNameList.FullAccess, userId) ||
                (
                    (
                        entity.CreatedById == userId ||
                        // or if the user is in the same department
                        (
                            departmentExpression.Invoke(entity) != null &&
                            departmentExpression.Invoke(entity).UserLinks.Any(x => x.UserId == userId)
                        ) ||
                        _db.FlowTransactions.Where(x => x.EntityId == entity.Id)
                            .Where(x => x.Value == DefaultFlowState.Submitted)
                            .Any(x => x.UserId == userId)
                    ) &&
                    EnsureUserHasPermissionSqlFunction.Call(permissions.Submit, userId)
                ),
            DefaultFlowState.Approved => (entity, userId) =>
                EnsureUserHasPermissionSqlFunction.Call(permissions.Intermediate, userId) &&
                !EnsureUserHasPermissionSqlFunction.Call(permissions.Immediate, userId),
            DefaultFlowState.Rejected => (entity, userId) =>
                EnsureUserHasPermissionSqlFunction.Call(permissions.Intermediate, userId),
            DefaultFlowState.ApprovedImmediate => (entity, userId) =>
                EnsureUserHasPermissionSqlFunction.Call(permissions.Immediate, userId),
            DefaultFlowState.ApprovedFinal or DefaultFlowState.RejectedFinal => (entity, userId) =>
                EnsureUserHasPermissionSqlFunction.Call(permissions.Approve, userId),
            _ => throw new InvalidEnumArgumentException("Invalid approval flow state.")
        };
    }

    protected override Expression<Func<T, Guid, Guid, bool>> RequiredDepartmentSignatoryAuthorization(
        string nextState)
    {
        switch (nextState)
        {
            case DefaultFlowState.Approved:
            case DefaultFlowState.ApprovedImmediate:
            case DefaultFlowState.Rejected:
                return (entity, userId, departmentId) => true;

            default:
                return (entity, userId, departmentId) => false;
        }
    }

    protected override async Task OnMove(T entity, string state)
    {
        switch (state)
        {
            case DefaultFlowState.Submitted:
                await NotifyNextApproval(entity);
                await NotifyForImmediateApproval(entity);
                break;

            case DefaultFlowState.Approved:
                await NotifyForImmediateApproval(entity);
                break;

            case DefaultFlowState.Rejected:
                await NotifyForRejection(entity);
                break;

            case DefaultFlowState.RejectedFinal:
                await NotifyForRejection(entity);
                break;

            case DefaultFlowState.ApprovedImmediate:
                await NotifyForFinalApproval(entity);
                break;

            case DefaultFlowState.ApprovedFinal:
                await NotifyFinalApprovalIsDone(entity);
                break;
        }
    }

    private async Task NotifyNextApproval(T entity)
    {
        var users = GetMovingUsers(entity.Id, DefaultFlowState.Approved)
            .Union(GetMovingUsers(entity.Id, DefaultFlowState.ApprovedFinal))
            .Select(UserSimpleDto.Mapper(HelperFunctions.GetLanguageCode()))
            .ToArray();

        if (!users.Any()) return;

        await NotifyForApproval(entity, users);
    }

    private async Task NotifyForRejection(T entity)
    {
        var users = GetMovingUsers(entity.Id, DefaultFlowState.Submitted)
            .Select(UserSimpleDto.Mapper(HelperFunctions.GetLanguageCode()))
            .ToArray();

        if (!users.Any()) return;

        await NotifyForRejection(entity, users);
    }

    private async Task NotifyForImmediateApproval(T entity)
    {
        var users = GetMovingUsers(entity.Id, DefaultFlowState.ApprovedImmediate)
            .Select(UserSimpleDto.Mapper(HelperFunctions.GetLanguageCode()))
            .ToArray();

        if (!users.Any()) return;

        await NotifyForImmediateApproval(entity, users);
    }

    private async Task NotifyFinalApprovalIsDone(T entity)
    {
        var permissions = GetPermissions();
        var departmentExpression = GetEntityDepartment();


        var users = _db.Users
            .AsExpandable()
            .Where(x =>
                EnsureUserHasPermissionSqlFunction.Call(permissions.Approve, x.Id) ||
                EnsureUserHasPermissionSqlFunction.Call(PermissionNameList.FullAccess, x.Id) ||
                (
                    (entity.CreatedById == x.Id ||
                     (
                         departmentExpression.Invoke(entity) != null &&
                         departmentExpression.Invoke(entity).UserLinks.Any(link => link.UserId == x.Id)
                     ) ||
                     _db.FlowTransactions
                         .Where(tx => tx.EntityId == entity.Id && tx.Value == DefaultFlowState.Submitted)
                         .Any(tx => tx.UserId == x.Id)) &&
                    EnsureUserHasPermissionSqlFunction.Call(permissions.Submit, x.Id)
                )||
                EnsureUserHasPermissionSqlFunction.Call(permissions.Intermediate, x.Id) ||
                EnsureUserHasPermissionSqlFunction.Call(permissions.Immediate, x.Id)
            )
            .Select(UserSimpleDto.Mapper(HelperFunctions.GetLanguageCode()))
            .ToArray();

        if (!users.Any()) return;

        await NotifyFinalApprovalIsDone(entity, users);
    }

    private async Task NotifyForFinalApproval(T entity)
    {
        var users = GetMovingUsers(entity.Id, DefaultFlowState.ApprovedFinal)
            .Select(UserSimpleDto.Mapper(HelperFunctions.GetLanguageCode()))
            .ToArray();

        if (!users.Any()) return;

        await NotifyForFinalApproval(entity, users);
    }

    protected record DefaultFlowPermissions(
        string Submit,
        string Intermediate,
        string Immediate,
        string Approve);
}
