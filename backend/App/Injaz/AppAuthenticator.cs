using System;
using System.Linq;
using System.Net.Http;
using System.Threading.Tasks;
using Injaz.Core;
using Injaz.Core.Dtos.Setting;
using Injaz.Core.Exceptions;
using Injaz.Core.Extensions;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Resources.Shared;
using Injaz.Core.Services;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Localization;
using MNMWebApp.OAuth;
using MNMWebApp.OAuth.Authenticators;
using Newtonsoft.Json;
using Newtonsoft.Json.Linq;

namespace Injaz;

public class AppAuthenticator : DefaultAuthenticator<User, Guid>
{
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly SettingDto _appSetting;
    private readonly UserManager<User> _userManager;
    private readonly SignInManager<User> _signInManager;


    public AppAuthenticator(
        UserManager<User> userManager,
        SignInManager<User> signInManager,
        OAuthService oAuthService,
        IStringLocalizer<SharedResource> localizer,
        AppSettingService appSettingService
    ) : base(userManager, signInManager, oAuthService)
    {
        _localizer = localizer;
        _appSetting = appSettingService.Get();
        _userManager = userManager;
        _signInManager = signInManager;
    }

    public override Task<AuthResult> Authenticate(string username, string password)
    {
        username = username.ToLower().Trim();

        if (username.All(char.IsDigit))
        {
            return AuthenticateByEmployeeNumber(username, password);
        }


        if (username == "app_office_365")
        {
            return AuthenticateByOffice365(password);
        }

        if (_appSetting.LdapSetting.IsEnabled &&
            (username.EndsWith(_appSetting.LdapSetting.Domain) || !username.Contains('@')))
        {
            return AuthenticateByLdap(username, password);
        }

        return base.Authenticate(username, password);
    }

    private async Task<AuthResult> AuthenticateByEmployeeNumber(string employeeNumber, string password)
    {
        var usersWithEmployeeNumber = await _userManager.Users
            .Where(u => u.EmployeeNumber == employeeNumber)
            .ToListAsync();

        // Check for duplicate employee numbers
        if (usersWithEmployeeNumber.Count > 1)
        {
            throw new GenericException
            {
                Messages = new string[] { _localizer["duplicated_employee_number_found_please_login_with_email"] }
            };
        }

        // If no user found with this employee number, return generic exception to try other authentication methods
        if (usersWithEmployeeNumber.Count == 0)
        {
            throw new GenericException
            {
                Messages = new string[] { _localizer["employee_number_was_not_found_please_login_with_email"] }
            };
        }

        // At this point we have exactly one user with this employee number
        var userByEmployeeNumber = usersWithEmployeeNumber.First();

        return await base.Authenticate(userByEmployeeNumber.Email, password);

    }

    private async Task<AuthResult> AuthenticateByLdap(string username, string password)
    {
        var settings = _appSetting.LdapSetting;

        var (success, fullName) = AppHelperFunctions.LdapLogin(username, password, settings);
        if (!success) return new AuthResult(null, false, _localizer["username_or_password_is_incorrect"]);

        username = username.Contains('@') ? username.Split("@")[0] : username;
        var userId = await PersistLdapUser(username + "@" + settings.Domain, fullName, "", User.TypeLdap);
        var refreshToken = await OAuthService.GenerateAndSaveRefreshTokenForUser(userId.ToString());

        return new AuthResult(refreshToken, true, "");
    }

    private async Task<AuthResult> AuthenticateByOffice365(string accessToken)
    {
        var url = _appSetting.Office365Setting.CheckUrl;
        var domain = _appSetting.Office365Setting.Domain;

        var request = new HttpRequestMessage(HttpMethod.Get, url);
        request.Headers.Add("Authorization", $"Bearer {accessToken}");

        using var client = new HttpClient();

        var response = await client.SendAsync(request);

        var responseStr = await response.Content.ReadAsStringAsync();
        var responseJson = JsonConvert.DeserializeObject<JObject>(responseStr)!;

        if (!response.IsSuccessStatusCode)
        {
            return new AuthResult(null, false, responseJson.GetValue("error")!.Value<string>("message"));
        }

        var email = responseJson.Value<string>("userPrincipalName");
        var firstName = responseJson.Value<string>("givenName");
        var lastName = responseJson.Value<string>("surname");

        // Check domain name.
        var receivedDomain = email?.Split("@")[^1] ?? "";
        if (receivedDomain != domain)
        {
            return new AuthResult(null, false, _localizer["invalid_domain_name"].Value);
        }


        var userId = await PersistLdapUser(
            email,
            $"{firstName} {lastName}",
            "",
            User.TypeOffice365
        );

        var refreshToken = await OAuthService.GenerateAndSaveRefreshTokenForUser(userId.ToString());

        return new AuthResult(refreshToken, true, "");
    }

    private async Task<Guid> PersistLdapUser(
        string email,
        string name,
        string employeeNumber,
        string userType
    )
    {
        var user = await UserManager.FindByEmailAsync(email);
        if (user != null)
        {
            // Update the user name and employee number.
            user.NameAr = name;
            user.NameEn = name;
            user.EmployeeNumber = employeeNumber;
            user.Type = userType;

            // Persist current permissions.
            // var currentPermissions = (await UserManager.GetClaimsAsync(user))
            //     .Where(x => x.Type.Equals(Constants.ClaimTypes.Permission))
            //     .Select(x => x.Value)
            //     .ToArray();

            await UserManager.UpdateAsync(user, null /*, currentPermissions*/);

            return user.Id;
        }

        user = new User
        {
            NameAr = name,
            NameEn = name,
            UserName = email,
            Email = email,
            Type = userType,
            EmployeeNumber = employeeNumber,
            Status = User.StatusActive
        };

        await UserManager.Create2Async(user, null);

        return user.Id;
    }
}
