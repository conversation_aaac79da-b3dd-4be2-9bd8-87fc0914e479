using Injaz.Core.Dtos.Misc;
using Injaz.Core.Dtos.StatisticalReport;
using Injaz.Core.Models.DomainClasses.App;

namespace Injaz.ModuleStatisticalReport;

public interface IRepository
{
    /// <summary>
    /// Should return the report + categories + links + results.
    /// </summary>
    /// <param name="id"></param>
    /// <returns></returns>
    StatisticalReport FindById(Guid id);

    TableResultDto<StatisticalReportListDto> GetList(
        string keyword,
        Guid[]? departmentIds,
        int pageNumber,
        int pageSize,
        int? initialYear,
        string[]? cycles,
        string? status,
        Guid currentUserId,
        bool currentUserHasStatisticalReportPermission,
        bool currentUserHasStatisticalReportLockAndUnlockPermission,
        bool currentUserHasStatisticalReportPublishPermission,
        bool currentUserHasStatisticalReportWritePermission,
        bool currentUserHasStatisticalReportDataEntryPermission,
        bool currentUserHasFullAccessPermission,
        string? orderBy
    );

    StatisticalReportGetDto GetById(Guid id);

    StatisticalReportEditDto GetForEditById(Guid id);

    StatisticalReportForDataEntryDto GetForDataEntryById(Guid id, Guid userId, bool hasFullAccessPermission);

    IEnumerable<StatisticalReportCategory> GetCategories(Guid id);

    IEnumerable<StatisticalReportCategoryResult> FindResultsById(Guid[] resultIds);
    IEnumerable<StatisticalReportCategoryResult> FindResultsByReportId(Guid reportId);

    void CreateItems(IEnumerable<StatisticalReport> items);

    void UpdateItems(IEnumerable<StatisticalReport> items);

    void CreateResults(IEnumerable<StatisticalReportCategoryResult> items);

    void UpdateResults(IEnumerable<StatisticalReportCategoryResult> items);

    void RemoveById(Guid id);

    void RemoveCategories(IEnumerable<StatisticalReportCategory> items);
    void CreateCategories(IEnumerable<StatisticalReportCategory> item);
    void CreateCategory(StatisticalReportCategory item);
    void CreateDepartment(StatisticalReportCategoryDepartmentLink item);
    void RemoveDepartment(IEnumerable<StatisticalReportCategoryDepartmentLink> items);

    bool HasAnyResults(IEnumerable<StatisticalReportCategory> categories);

    void Commit();
}
