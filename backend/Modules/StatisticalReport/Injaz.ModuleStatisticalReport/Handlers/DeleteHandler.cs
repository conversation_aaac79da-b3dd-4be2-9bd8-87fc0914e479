using Injaz.ModuleStatisticalReport.Core.Commands;
using MediatR;

namespace Injaz.ModuleStatisticalReport.Handlers;

public class DeleteHandler : IRequestHandler<DeleteStatisticalReportCommand>
{
    private readonly IRepository _repo;

    public DeleteHandler(
        IRepository repo
    )
    {
        _repo = repo;
    }

    public Task<Unit> Handle(DeleteStatisticalReportCommand request, CancellationToken cancellationToken)
    {
        _repo.RemoveById(request.Id);
        _repo.Commit();
        
        return Task.FromResult(Unit.Value);
    }
}