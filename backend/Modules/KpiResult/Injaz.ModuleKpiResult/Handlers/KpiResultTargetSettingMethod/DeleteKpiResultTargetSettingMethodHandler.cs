using Injaz.Core.Exceptions;
using Injaz.Core.Resources.Shared;
using Injaz.ModuleKpiResult.Core.Commands.KpiResultTargetSettingMethod;
using MediatR;
using Microsoft.Extensions.Localization;

namespace Injaz.ModuleKpiResult.Handlers.KpiResultTargetSettingMethod;

public class DeleteKpiResultTargetSettingMethodHandler : IRequestHandler<DeleteKpiResultTargetSettingMethodCommand>
{
    private readonly DbContext _db;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public DeleteKpiResultTargetSettingMethodHandler(
        DbContext db,
        IStringLocalizer<SharedResource> localizer
    )
    {
        _db = db;
        _localizer = localizer;
    }

    public Task<Unit> Handle(DeleteKpiResultTargetSettingMethodCommand command, CancellationToken cancellationToken)
    {
        var data = _db
            .KpiResultTargetSettingMethods
            .Where(x => x.Id == command.Id)
            .Select(x => new { Item = x, IsLinkedToOtherResources = x.KpiResults.Any() })
            .FirstOrDefault();

        if (data == null) throw new ItemNotFoundException();

        // Ensure that the item does not have any links under it.
        if (data.IsLinkedToOtherResources)
            throw new GenericException
                { Messages = new string[] { _localizer["cannot_delete_item_there_are_items_under_it"] } };

        _db.KpiResultTargetSettingMethods.Remove(data.Item);

        _db.SaveChanges();

        return Task.FromResult(Unit.Value);
    }
}
