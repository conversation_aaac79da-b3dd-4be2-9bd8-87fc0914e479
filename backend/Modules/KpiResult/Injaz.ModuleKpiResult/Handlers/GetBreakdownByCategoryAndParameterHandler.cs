using LinqKit;
using Injaz.Core;
using Injaz.Core.DtoMappingStrategies;
using Injaz.Core.Dtos.KpiResult;
using Injaz.Core.Exceptions;
using Injaz.Core.Extensions;
using Injaz.ModuleKpiResult.Core.Queries;
using MediatR;

namespace Injaz.ModuleKpiResult.Handlers;

public class
    GetBreakdownByCategoryAndParameterHandler : IRequestHandler<GetKpiResultBreakdownByCategoryAndParameterQuery,
        KpiResultBreakdownDto>
{
    private readonly DbContext _db;

    public GetBreakdownByCategoryAndParameterHandler(DbContext db)
    {
        _db = db;
    }

    public Task<KpiResultBreakdownDto> Handle(GetKpiResultBreakdownByCategoryAndParameterQuery query,
        CancellationToken cancellationToken)
    {
        var predicate = HelperExpression
            .IsInvolvedWithKpiResult(query.CurrentUserId, _db.Departments, true)
            .Or(x => query.CurrentUserHasFullAccessPermission);

        var item = _db
            .KpiResults
            .AsExpandable()
            .Where(x => x.Id == query.Id)
            .Where(x => predicate.Invoke(x))
            .Map(new KpiResultBreakdownMappingStrategy(
                HelperFunctions.GetLanguageCode(),
                query.CategoryId,
                query.Parameter
            ))
            .FirstOrDefault();

        if (item == null)
        {
            throw new ItemNotFoundException();
        }

        return Task.FromResult(item.FirstOrDefault());
    }
}
