using System.Reflection;
using Injaz.Core;
using Injaz.Core.Evaluate.Extensions;
using Injaz.Core.Flow.Extensions;
using Injaz.ModuleKpiResult.HostedServices;
using MediatR;
using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.DependencyInjection;

namespace Injaz.ModuleKpiResult;

public static class KpiResultModule
{
    public static void Setup(IServiceCollection services, string connectionString)
    {
        services.AddMediatR(typeof(KpiResultModule));
        services.AddFlowServices(typeof(KpiResultModule).Assembly);

        services.AddEvaluationConfigurations();

        // Hosted services.
        services.AddHostedService<KpiResultReminderHostedService>();

        // Add DbContext.
        var migrationsAssembly = typeof(HelperFunctions).GetTypeInfo().Assembly.GetName().Name;
        services
            .AddDbContext<DbContext>(options =>
                options.UseSqlServer(
                    connectionString,
                    b => b.MigrationsAssembly(migrationsAssembly))
            );
    }
}
