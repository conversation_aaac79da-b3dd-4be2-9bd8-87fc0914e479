using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Permission;
using Injaz.ModuleKpiResult.Core.Queries.KpiResultPeriodEvaluate;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using MNMWebApp.Binders;

namespace Injaz.ModuleKpiResult.Controllers;

[Route("/kpi-result-period-evaluate-report")]
[Authorize(Policy = PermissionNameList.KpiResultPeriodExportEvaluation)]
public class KpiResultPeriodEvaluateReportController : Controller
{
    private readonly ISender _mediator;
    private readonly UserManager<User> _userManager;

    public KpiResultPeriodEvaluateReportController(ISender mediator, UserManager<User> userManager)
    {
        _mediator = mediator;
        _userManager = userManager;
    }

    [HttpGet("export")]
    public async Task<IActionResult> Export([BindQueryStringJson] ExportReportQuery settings)
    {
        settings.CurrentUserId = new Guid(_userManager.GetUserId(User));

        var fileContentBytes = await _mediator.Send(settings);

        return File(
            fileContents: fileContentBytes,
            contentType: "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"
        );
    }
}
