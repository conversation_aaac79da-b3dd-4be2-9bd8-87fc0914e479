using Injaz.Core.Dtos.KpiResult;
using Injaz.Core.Dtos.Misc;
using MediatR;

namespace Injaz.ModuleKpiResult.Core.Queries;

public class GetKpiDynamicDataEntryRequestListQuery : IRequest<TableResultDto<KpiDynamicDataEntryRequestDto>>
{
    public string Keyword { get; set; }
    public string Status { get; set; }
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public Guid CurrentUserId { get; set; }
}
