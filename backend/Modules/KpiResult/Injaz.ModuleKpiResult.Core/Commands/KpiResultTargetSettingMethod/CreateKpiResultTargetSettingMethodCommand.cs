using System.ComponentModel.DataAnnotations;
using Injaz.Core.Dtos.KpiResultTargetSettingMethod;
using MediatR;

namespace Injaz.ModuleKpiResult.Core.Commands.KpiResultTargetSettingMethod;

public class CreateKpiResultTargetSettingMethodCommand : IRequest<KpiResultTargetSettingMethodGetDto>
{
    [Display(Name = "name_in_arabic")]
    [Required(ErrorMessage = "0_is_required")]
    public string NameAr { get; set; }

    [Display(Name = "name_in_english")] public string NameEn { get; set; }

    [Display(Name = "show_extra_target_fields")]
    public bool ShowExtraTargetFields { get; set; }
}
