using LinqKit;
using Microsoft.AspNetCore.Mvc;
using MNMWebApp.Controllers;
using MNMWebApp.Binders;
using TrainingProgramModel = Injaz.Core.Models.DomainClasses.App.TrainingProgram;
using Microsoft.AspNetCore.Authorization;
using Injaz.Core;
using Injaz.Core.Dtos.TrainingProgram;
using Injaz.Core.Extensions;
using Injaz.Core.Permission;

namespace Injaz.ModuleInnovator.Controllers;

public partial class InnovatorController
{
    [HttpGet]
    [Route("/innovator/training-program/{programId:guid}")]
    [Authorize(Policy = PermissionNameList.Innovator)]
    public async Task<IActionResult> GetTrainingProgram(Guid programId, bool forEdit = false)
    {
        var canAccessTrainingProgram = await CanAccessTrainingProgram(programId);
        if(!canAccessTrainingProgram.Succeeded)
        {
            return canAccessTrainingProgram.ErrorResult;
        }

        var item = forEdit
            ? _appDataContext.TrainingPrograms
                .AsExpandable()
                .Select(TrainingProgramEditDto.Mapper(HelperFunctions.GetLanguageCode()))
                .FirstOrDefault(p => p.Id.Equals(programId)) as object
            : _appDataContext.TrainingPrograms
                .AsExpandable()
                .Select(TrainingProgramGetDto.Mapper(HelperFunctions.GetLanguageCode()))
                .FirstOrDefault(p => p.Id.Equals(programId));

        if (item == null)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 404,
                messages: new string[] { _localizer["item_not_found"] }
            );
        }

        return this.GetResponseObject(extra: item);
    }

    [HttpPost]
    [Route("/innovator/training-program/{id:guid}")]
    [Authorize(Policy = PermissionNameList.Innovator)]
    public async Task<IActionResult> CreateTrainingProgram(Guid id, [BindBodySingleJson] TrainingProgramCreateDto program)
    {
        var innovator = _appDataContext.Innovators.Find(id);
        if (innovator == null)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 404,
                messages: new string[] { _localizer["item_not_found"] }
            );
        }

        var hasPermission = await CanAccess(id);
        if(!hasPermission.Succeeded)
        {
            return hasPermission.ErrorResult;
        }

        return CreateOrUpdate(program, id);
    }

    [HttpPut]
    [Route("/innovator/training-program")]
    [Authorize(Policy = PermissionNameList.Innovator)]
    public async Task<IActionResult> UpdateTrainingProgram([BindBodySingleJson] TrainingProgramEditDto program)
    {
        var canAccessTrainingProgram = await CanAccessTrainingProgram(program.Id);
        if(!canAccessTrainingProgram.Succeeded)
        {
            return canAccessTrainingProgram.ErrorResult;
        }
        return CreateOrUpdate(program);
    }

    [HttpDelete]
    [Route("/innovator/training-program/{programId:guid}")]
    [Authorize(Policy = PermissionNameList.Innovator)]
    public async Task<IActionResult> DeleteTrainingProgram(Guid programId)
    {
        var canAccessTrainingProgram = await CanAccessTrainingProgram(programId);
        if(!canAccessTrainingProgram.Succeeded)
        {
            return canAccessTrainingProgram.ErrorResult;
        }

        var data = _appDataContext.TrainingPrograms.Find(programId);


        if (data == null)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 404,
                messages: new string[] { _localizer["item_not_found"] }
            );
        }

        _appDataContext.TrainingPrograms.Remove(data);
        _appDataContext.SaveChanges();

        return this.GetResponseObject(messages: new string[] { _localizer["item_removed_successfully"] });
    }

    private IActionResult CreateOrUpdate(TrainingProgramCreateDto trainingProgram, Guid? innovatorId = null)
    {
        if (!ModelState.IsValid)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: ModelState.SelectMany(x => x.Value!.Errors.Select(y => y.ErrorMessage)).ToArray()
            );
        }

        TrainingProgramModel item;
        if (trainingProgram is TrainingProgramEditDto trainingProgramEditDto)
        {
            item = _appDataContext.TrainingPrograms.Find(trainingProgramEditDto.Id);

            if (item == null)
            {
                return this.GetResponseObject(
                    success: 0,
                    statusCode: 404,
                    messages: new string[] { _localizer["item_not_found"] }
                );
            }
        }
        else
        {
            item = new TrainingProgramModel {
                InnovatorId = innovatorId!.Value
            };
            _appDataContext.TrainingPrograms.Add(item);
        }

        // Fill the fields.
        item.NameAr = trainingProgram.NameAr.Trim();
        item.NameEn = trainingProgram.NameEn?.Trim() ?? trainingProgram.NameAr.Trim();
        item.Year = trainingProgram.Year;
        item.Hours = trainingProgram.Hours;


        _appDataContext.SaveChanges();

        return this.GetResponseObject
        (
            extra: _appDataContext
                .TrainingPrograms
                .AsExpandable()
                .Select(TrainingProgramGetDto.Mapper(HelperFunctions.GetLanguageCode()))
                .First(x => x.Id.Equals(item.Id))
        );
    }

    private async Task<(bool Succeeded, IActionResult ErrorResult)> CanAccessTrainingProgram(Guid programId)
    {
        var canManageInnovation = await this.EnsureUserHasPermission(PermissionNameList.Innovation);
        if(canManageInnovation)
        {
            return (true, null);
        }

        //get current logged in user
        var user = await _userManager.GetUserAsync(User);

        //check if the user has program
        var hasProgram = _appDataContext
            .Innovators
            .Where(x => x.EmployeeNumber == user.EmployeeNumber)
            .Select(x => x.TrainingPrograms.Any(a => a.Id.Equals(programId)))
            .FirstOrDefault();



        if(!hasProgram)
        {
            return (false, this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: new string[] { _localizer["cannot_access_this_resource"] }
            ));
        }
        return (true, null);
    }

}
