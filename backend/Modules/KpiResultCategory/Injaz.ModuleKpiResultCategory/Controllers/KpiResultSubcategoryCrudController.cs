using Injaz.Core.Permission;
using Injaz.Core.Resources.Shared;
using Injaz.ModuleKpiResultCategory.Core.Commands;
using Injaz.ModuleKpiResultCategory.Core.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using MNMWebApp.Binders;
using MNMWebApp.Controllers;

namespace Injaz.ModuleKpiResultCategory.Controllers;

public class KpiResultSubcategoryCrudController : Controller
{
    private readonly IMediator _mediator;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public KpiResultSubcategoryCrudController(
        IMediator mediator,
        IStringLocalizer<SharedResource> localizer
    )
    {
        _mediator = mediator;
        _localizer = localizer;
    }

    [HttpGet]
    [Route("/kpi-result-subcategory")]
    [Authorize(Policy = PermissionNameList.KpiResultCategory)]
    public async Task<IActionResult> List(
        string keyword = "",
        Guid[] categoryIds = null,
        int pageNumber = 0,
        int pageSize = 20
    )
    {
        var response = await _mediator.Send(new GetKpiResultSubcategoryListQuery
        {
            Keyword = keyword,
            CategoryIds = categoryIds,
            PageNumber = pageNumber,
            PageSize = pageSize
        });

        return this.GetResponseObject(extra: response);
    }

    [HttpGet]
    [Route("/kpi-result-subcategory/{id:guid}")]
    [Authorize(Policy = PermissionNameList.KpiResultCategory)]
    public async Task<IActionResult> Get(
        Guid id,
        bool forEdit = false
    )
    {
        var item = await _mediator.Send(forEdit
            ? new GetKpiResultSubcategoryForEditByIdQuery { Id = id }
            : new GetKpiResultSubcategoryByIdQuery { Id = id });

        return this.GetResponseObject(extra: item);
    }

    [HttpPost]
    [Route("/kpi-result-subcategory")]
    [Authorize(Policy = PermissionNameList.KpiResultCategory)]
    public async Task<IActionResult> Create(
        [BindBodySingleJson] CreateKpiResultSubcategoryCommand kpiResultSubcategory
    )
    {
        var response = await _mediator.Send(kpiResultSubcategory);
        return this.GetResponseObject(extra: response);
    }

    [HttpPut]
    [Route("/kpi-result-subcategory")]
    [Authorize(Policy = PermissionNameList.KpiResultCategory)]
    public async Task<IActionResult> Update(
        [BindBodySingleJson] UpdateKpiResultSubcategoryCommand kpiResultSubcategory
    )
    {
        var response = await _mediator.Send(kpiResultSubcategory);
        return this.GetResponseObject(extra: response);
    }

    [HttpDelete]
    [Route("/kpi-result-subcategory/{id:guid}")]
    [Authorize(Policy = PermissionNameList.KpiResultCategory)]
    public async Task<IActionResult> Delete(
        Guid id
    )
    {
        await _mediator.Send(new DeleteKpiResultSubcategoryCommand
        {
            Id = id,
        });
        return this.GetResponseObject(messages: new string[] { _localizer["item_removed_successfully"] });
    }
}
