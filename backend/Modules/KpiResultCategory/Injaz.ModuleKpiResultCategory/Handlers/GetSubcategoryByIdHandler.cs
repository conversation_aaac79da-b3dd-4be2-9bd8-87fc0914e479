using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.KpiResultSubcategory;
using Injaz.Core.Exceptions;
using Injaz.ModuleKpiResultCategory.Core.Queries;
using MediatR;

namespace Injaz.ModuleKpiResultCategory.Handlers;

public class GetSubcategoryByIdHandler : IRequestHandler<GetKpiResultSubcategoryByIdQuery, KpiResultSubcategoryGetDto>
{
    private readonly DbContext _db;

    public GetSubcategoryByIdHandler(
        DbContext db
    )
    {
        _db = db;
    }

    public Task<KpiResultSubcategoryGetDto> Handle(GetKpiResultSubcategoryByIdQuery request,
        CancellationToken cancellationToken)
    {
        var item = _db.KpiResultSubcategories
            .AsExpandable()
            .Select(KpiResultSubcategoryGetDto.Mapper(HelperFunctions.GetLanguageCode()))
            .FirstOrDefault(x => x.Id.Equals(request.Id));

        if (item == null)
        {
            throw new ItemNotFoundException();
        }

        return Task.FromResult(item);
    }
}
