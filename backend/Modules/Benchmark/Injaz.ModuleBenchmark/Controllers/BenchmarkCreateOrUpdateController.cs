using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.Benchmark;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Permission;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MNMWebApp.Binders;
using MNMWebApp.Controllers;
using BenchmarkModel = Injaz.Core.Models.DomainClasses.App.Benchmark;

namespace Injaz.ModuleBenchmark.Controllers;

public partial class BenchmarkController
{
    [HttpPost]
    [Route("/benchmark")]
    [Authorize(Policy = PermissionNameList.BenchmarkWrite)]
    public IActionResult Create([BindBodySingleJson] BenchmarkCreateDto benchmark)
    {
        return CreateOrUpdate(benchmark);
    }

    [HttpPut]
    [Route("/benchmark")]
    [Authorize(Policy = PermissionNameList.BenchmarkWrite)]
    public IActionResult Update([BindBodySingleJson] BenchmarkEditDto benchmark)
    {
        return CreateOrUpdate(benchmark);
    }

    private IActionResult CreateOrUpdate(BenchmarkCreateDto benchmark)
    {
        if (!Validate(benchmark, out var errorResult))
        {
            return errorResult;
        }

        Clean(benchmark);

        BenchmarkModel item;

        if (benchmark is BenchmarkEditDto editDto)
        {
            item = _appDataContext
                .Benchmarks
                .Find(editDto.Id);

            // Ensure the item exists.
            if (item == null)
            {
                return this.GetResponseObject(
                    success: 0,
                    statusCode: 404,
                    messages: new string[] { _localizer["item_not_found"] }
                );
            }

            // Remove existing links.
            _appDataContext.RemoveRange(
                _appDataContext.BenchmarkStrategicGoalLinks.Where(x => x.BenchmarkId == item.Id));
            _appDataContext.RemoveRange(_appDataContext.BenchmarkOperationLinks.Where(x => x.BenchmarkId == item.Id));
            _appDataContext.RemoveRange(_appDataContext.BenchmarkOtherManagements.Where(x => x.BenchmarkId == item.Id));
            _appDataContext.RemoveRange(
                _appDataContext.BenchmarkBenchmarkRequestReasonLinks.Where(x => x.BenchmarkId == item.Id));
            _appDataContext.RemoveRange(
                _appDataContext.BenchmarkBenchmarkSelectionReasonLinks.Where(x => x.BenchmarkId == item.Id));
            _appDataContext.RemoveRange(_appDataContext.BenchmarkVisitors.Where(x => x.BenchmarkId == item.Id));
            _appDataContext.RemoveRange(_appDataContext.BenchmarkKpiLinks.Where(x => x.BenchmarkId == item.Id));
        }
        else
        {
            item = new BenchmarkModel();
            _appDataContext.Add(item);
        }

        // Update:
        item.VisitDate = benchmark.VisitDate!.Value;
        item.Language = benchmark.Language;
        item.DepartmentId = benchmark.Department.Id;
        item.Type = benchmark.Type;
        item.Method = benchmark.Method;
        item.ManagementType = benchmark.ManagementType;
        item.EntityType = benchmark.EntityType;
        item.EntityName = benchmark.EntityName;
        item.PartnerId = benchmark.Partner?.Id;
        item.OtherRequestReasons = benchmark.OtherRequestReasons;
        item.OtherSelectionReasons = benchmark.OtherSelectionReasons;
        item.CoordinatorEmployeeNumber = benchmark.CoordinatorEmployeeNumber;
        item.CoordinatorFullName = benchmark.CoordinatorFullName;
        item.CoordinatorRank = benchmark.CoordinatorRank;
        item.CoordinatorEmail = benchmark.CoordinatorEmail;
        item.CoordinatorPhone = benchmark.CoordinatorPhone;
        item.CoordinatorOfficeNumber = benchmark.CoordinatorOfficeNumber;
        item.Agenda = benchmark.Agenda;

        item.GoalLinks = benchmark.Goals.Select(x => new BenchmarkStrategicGoalLink
        {
            GoalId = x.Id
        }).ToList();

        item.OperationLinks = benchmark.Operations?.Select(x => new BenchmarkOperationLink
        {
            OperationId = x.Id
        }).ToList();

        item.OtherManagements = benchmark.OtherManagements?.Select(x => new BenchmarkOtherManagement
        {
            Type = x.Type,
            Detail = x.Detail
        }).ToList();

        item.RequestReasonLinks = benchmark.RequestReasons?.Select(x => new BenchmarkBenchmarkRequestReasonLink
        {
            ReasonId = x.Id
        }).ToList();

        item.SelectionReasonLinks = benchmark.SelectionReasons?.Select(x => new BenchmarkBenchmarkSelectionReasonLink
        {
            ReasonId = x.Id
        }).ToList();

        item.Visitors = benchmark.Visitors.Select(x => new BenchmarkVisitor
        {
            EmployeeNumber = x.EmployeeNumber,
            Rank = x.Rank,
            FullName = x.FullName,
            Department = x.Department,
            EmploymentTitle = x.EmploymentTitle,
            Description = x.Description,
            Phone = x.Phone,
            Email = x.Email
        }).ToList();

        item.KpiResultLinks = benchmark.KpiResults?.Select(x => new BenchmarkKpiResultLink
        {
            ResultId = x.Result.Id,
            PartnerResult = x.PartnerResult
        }).ToList();

        // Save
        _appDataContext.SaveChanges();

        var canAchievedBeNegative = _appSettingService.Get().KpiSetting.CanResultAchievementTakeNegativeValues;

        // Return the item using the appropriate dto.
        return this.GetResponseObject(extra: _appDataContext
            .Benchmarks
            .AsExpandable()
            .Select(BenchmarkGetDto.Mapper(HelperFunctions.GetLanguageCode(), canAchievedBeNegative))
            .First(x => x.Id.Equals(item.Id))
        );
    }

    private bool Validate(BenchmarkCreateDto benchmark, out IActionResult errorResult)
    {
        errorResult = null;

        // Ensure no errors.
        if (!ModelState.IsValid)
        {
            errorResult = this.GetResponseObject(success: 0,
                statusCode: 423,
                messages: ModelState
                    .SelectMany(x => x.Value!.Errors.Select(y => y.ErrorMessage))
                    .ToArray()
            );
            return false;
        }

        // Check validity of management type.
        // if (benchmark.ManagementType == BenchmarkModel.ManagementTypeOperation &&
        //     !benchmark.Operations.Any())
        // {
        //     errorResult = this.GetResponseObject(
        //         success: 0,
        //         statusCode: 423,
        //         messages: new string[] { _localizer["there_should_be_at_least_one_operation"] }
        //     );
        //     return false;
        // }

        // if (benchmark.ManagementType == BenchmarkModel.ManagementTypeOther &&
        //     !benchmark.OtherManagements.Any())
        // {
        //     errorResult = this.GetResponseObject(
        //         success: 0,
        //         statusCode: 423,
        //         messages: new string[] { _localizer["there_should_be_at_least_one_management_item"] }
        //     );
        //     return false;
        // }

        // Check the entity type and name:
        if (benchmark.EntityType == BenchmarkModel.EntityTypePartner && benchmark.Partner == null)
        {
            errorResult = this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: new string[] { _localizer["partner_is_required"] }
            );
            return false;
        }

        if (benchmark.EntityType is BenchmarkModel.EntityTypeCountry
                or BenchmarkModel.EntityTypeOther &&
            string.IsNullOrEmpty(benchmark.EntityName)
           )
        {
            errorResult = this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: new string[] { _localizer["entity_name_is_required"] }
            );
            return false;
        }

        // Request reasons.
        if (!(benchmark.RequestReasons?.Any() ?? false) && string.IsNullOrEmpty(benchmark.OtherRequestReasons))
        {
            errorResult = this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: new string[] { _localizer["you_should_mention_request_reasons"] }
            );
            return false;
        }

        // Selection reasons.
        // if (!(benchmark.SelectionReasons?.Any() ?? false) && string.IsNullOrEmpty(benchmark.OtherSelectionReasons))
        // {
        //     errorResult = this.GetResponseObject(
        //         success: 0,
        //         statusCode: 423,
        //         messages: new string[] { _localizer["you_should_mention_selection_reasons"] }
        //     );
        //     return false;
        // }

        return true;
    }

    private void Clean(BenchmarkCreateDto benchmark)
    {
        // Nullify other managements if management type is operation.
        if (benchmark.ManagementType == BenchmarkModel.ManagementTypeOperation)
        {
            benchmark.OtherManagements = null;
        }

        // Nullify operations if management type is other.
        if (benchmark.ManagementType == BenchmarkModel.ManagementTypeOther)
        {
            benchmark.Operations = null;
        }

        // Nullify entity name if entity type is partner. 
        if (benchmark.EntityType == BenchmarkModel.EntityTypePartner)
        {
            benchmark.EntityName = "";
        }

        // Nullify partner if entity type is not partner.
        else
        {
            benchmark.Partner = null;
        }

        // Clean duplicated results.
        benchmark.KpiResults = benchmark
            .KpiResults
            .GroupBy(x => new { x.Result.Year, x.Result.Kpi.Id })
            .Select(x => x.First());
    }
}