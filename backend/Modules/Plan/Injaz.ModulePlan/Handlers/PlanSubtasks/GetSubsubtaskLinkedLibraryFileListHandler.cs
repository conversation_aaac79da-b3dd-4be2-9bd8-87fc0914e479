using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.LibraryFile;
using Injaz.Core.Dtos.Misc;
using Injaz.Core.Flow.Services;
using Injaz.ModulePlan.Core.Queries.PlanSubtasks;
using MediatR;

namespace Injaz.ModulePlan.Handlers.PlanSubtasks;

public class GetSubsubtaskLinkedLibraryFileListHandler : IRequestHandler<GetSubsubtaskLinkedLibraryFileListQuery,
    TableResultDto<LibraryFileGetDto>>
{
    private readonly DbContext _db;

    public GetSubsubtaskLinkedLibraryFileListHandler(DbContext db) => _db = db;

    public Task<TableResultDto<LibraryFileGetDto>> Handle(
        GetSubsubtaskLinkedLibraryFileListQuery query,
        CancellationToken cancellationToken
    )
    {
        query.Keyword = query.Keyword?.Trim().ToLower() ?? "";

        var items = _db
            .PlanSubsubtaskLibraryFileLinks
            .AsExpandable()

            // Return nothing if the plan is not approved!
            .Where(x => x.PlanSubsubtask.Subtask.Task.Plan.FlowState == DefaultFlowState.ApprovedFinal)
            .Where(x => x.PlanSubsubtaskId.Equals(query.SubsubtaskId));

        var count = items.Count();

        items = items.Where(x => x.LibraryFile.NameAr.ToLower().Contains(query.Keyword) ||
                                 x.LibraryFile.NameEn.ToLower().Contains(query.Keyword));

        return Task.FromResult(new TableResultDto<LibraryFileGetDto>
        {
            Items = items
                .Select(x => x.LibraryFile)
                .Select(LibraryFileGetDto.Mapper(HelperFunctions.GetLanguageCode()))
                .OrderBy(x => x.Name)
                .Skip(query.PageNumber * query.PageSize)
                .Take(query.PageSize),
            Count = count,
            FilteredCount = items.Count()
        });
    }
}
