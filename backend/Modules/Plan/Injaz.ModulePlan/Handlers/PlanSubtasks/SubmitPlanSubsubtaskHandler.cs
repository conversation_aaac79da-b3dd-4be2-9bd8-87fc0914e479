using System.Threading;
using System.Threading.Tasks;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.ModulePlan.Core.Commands.PlanSubtasks;
using Injaz.ModulePlan.Services.Plans;
using MediatR;

namespace Injaz.ModulePlan.Handlers.PlanSubtasks;

public class SubmitPlanSubsubtaskHandler : IRequestHandler<SubmitPlanSubsubtaskCommand>
{
    private readonly PlanHelperService _planHelperService;

    public SubmitPlanSubsubtaskHandler(
        PlanHelperService planHelperService)
    {
        _planHelperService = planHelperService;
    }

    public async Task<Unit> Handle(SubmitPlanSubsubtaskCommand command, CancellationToken cancellationToken)
    {
        await _planHelperService.ProcessSubsubtaskApproval(
            command.SubsubtaskId,
            PlanSubsubtaskApproval.TypeSubmitted);

        return Unit.Value;
    }
}
