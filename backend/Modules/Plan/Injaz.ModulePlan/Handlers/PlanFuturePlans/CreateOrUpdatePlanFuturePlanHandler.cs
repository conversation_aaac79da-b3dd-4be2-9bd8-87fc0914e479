using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.PlanDtos.PlanFuturePlans;
using Injaz.Core.Exceptions;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Services;
using Injaz.ModulePlan.Core.Commands.PlanFuturePlans;
using MediatR;

namespace Injaz.ModulePlan.Handlers.PlanFuturePlans;

public class CreateOrUpdatePlanFuturePlanHandler
    : IRequestHandler<CreatePlanFuturePlanCommand, PlanFuturePlanGetDto>,
        IRequestHandler<UpdatePlanFuturePlanCommand, PlanFuturePlanGetDto>
{
    private readonly DbContext _db;
    private readonly ValidationService _validationService;

    public CreateOrUpdatePlanFuturePlanHandler(
        DbContext db,
        ValidationService validationService
    )
    {
        _db = db;
        _validationService = validationService;
    }

    public Task<PlanFuturePlanGetDto> Handle(CreatePlanFuturePlanCommand command,
        CancellationToken cancellationToken)
    {
        return CreateOrUpdate(command);
    }

    public Task<PlanFuturePlanGetDto> Handle(UpdatePlanFuturePlanCommand command,
        CancellationToken cancellationToken)
    {
        return CreateOrUpdate(command);
    }

    private Task<PlanFuturePlanGetDto> CreateOrUpdate(CreatePlanFuturePlanCommand command)
    {
        if (!_validationService.IsValid(command, out var errors))
            throw new GenericException { Messages = errors.Select(x => x.ErrorMessage).ToArray() };

        PlanFuturePlan item;

        if (command is UpdatePlanFuturePlanCommand updateCommand)
        {
            item = _db.PlanFuturePlans.Find(updateCommand.Id);
            if (item == null) throw new ItemNotFoundException();
        }
        else
        {
            item = new PlanFuturePlan();
            _db.PlanFuturePlans.Add(item);
        }

        item.NameAr = command.NameAr.Trim();
        item.NameEn = HelperFunctions.Default(command.NameEn?.Trim(), command.NameAr);

        _db.SaveChanges();

        return Task.FromResult(
            _db
                .PlanFuturePlans
                .AsExpandable()
                .Select(PlanFuturePlanGetDto.Mapper(HelperFunctions.GetLanguageCode()))
                .First(x => x.Id.Equals(item.Id))
        );
    }
}
