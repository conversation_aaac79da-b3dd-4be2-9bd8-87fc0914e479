using System;
using Injaz.Core.Dtos.LibraryFile;
using Injaz.Core.Dtos.Misc;
using MediatR;

namespace Injaz.ModulePlan.Core.Queries.PlanSubtasks;

public class GetSubsubtaskUnlinkedLibraryFileListQuery : IRequest<TableResultDto<LibraryFileGetDto>>
{
    public Guid Id { get; set; }

    public string Keyword { get; set; }

    public int PageNumber { get; set; }

    public int PageSize { get; set; }
}
