using System;
using Injaz.Core.Dtos.Misc;
using Injaz.Core.Dtos.PlanDtos.PlanTasks;
using MediatR;

namespace Injaz.ModulePlan.Core.Queries.PlanTasks;

public class GetPlanTaskListQuery : IRequest<TableResultDto<PlanTaskListDto>>
{
    public string Keyword { get; set; }
    public int[] Years { get; set; }
    public string AssigneeType { get; set; }
    public Guid[] DepartmentIds { get; set; }
    public bool IncludeChildDepartments { get; set; }
    public Guid[] TeamIds { get; set; }
    public Guid[] UserIds { get; set; }
    public Guid[] PlanIds { get; set; }
    public DateTime? From { get; set; }
    public DateTime? To { get; set; }
    public string ProgressStatus { get; set; } // in_progress, completed
    public int PageNumber { get; set; }
    public int PageSize { get; set; }
    public string OrderBy { get; set; }

    // When set to true, the base items would
    // include a filter based on passed `planIds`.
    // This is needed when one wants to fetch
    // the tasks of a plan but does not want
    // the filtered count to be affected.
    public bool ConsiderPlanFilterAsBase { get; set; }
}
