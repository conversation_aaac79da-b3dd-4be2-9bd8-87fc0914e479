using Injaz.Core.Dtos.Misc;
using Injaz.Core.Dtos.Notification;
using MediatR;

namespace Injaz.ModuleNotification.Core.Queries;

public class GetUserNotificationListQuery : IRequest<TableResultDto<NotificationForUserDto>>
{
    public Guid UserId { get; set; }
    public string Keyword { get; set; }

    public DateTime? From { get; set; }

    public DateTime? To { get; set; }

    public bool IsUnread { get; set; }

    public int PageNumber { get; set; }

    public int PageSize { get; set; }
}