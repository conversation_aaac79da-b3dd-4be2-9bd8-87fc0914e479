using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.ServiceProviderChannel;
using Injaz.Core.Exceptions;
using Injaz.Core.Services;
using Injaz.ModuleService.Core.ServiceProviderChannel.Commands;
using MediatR;
using ServiceProviderChannelModel = Injaz.Core.Models.DomainClasses.App.ServiceModel.ServiceProviderChannel;

namespace Injaz.ModuleService.ServiceProviderChannel.Handlers;

public class CreateOrUpdateServiceProviderChannelHandler
    : IRequestHandler<CreateServiceProviderChannelCommand, ServiceProviderChannelGetDto>,
        IRequestHandler<UpdateServiceProviderChannelCommand, ServiceProviderChannelGetDto>
{
    private readonly DbContext _db;
    private readonly ValidationService _validationService;

    public CreateOrUpdateServiceProviderChannelHandler(
        DbContext db,
        ValidationService validationService
    )
    {
        _db = db;
        _validationService = validationService;
    }

    public Task<ServiceProviderChannelGetDto> Handle(CreateServiceProviderChannelCommand command,
        CancellationToken cancellationToken)
    {
        return CreateOrUpdate(command);
    }

    public Task<ServiceProviderChannelGetDto> Handle(UpdateServiceProviderChannelCommand command,
        CancellationToken cancellationToken)
    {
        return CreateOrUpdate(command);
    }

    private Task<ServiceProviderChannelGetDto> CreateOrUpdate(CreateServiceProviderChannelCommand command)
    {
        if (!_validationService.IsValid(command, out var errors))
            throw new GenericException { Messages = errors.Select(x => x.ErrorMessage).ToArray() };

        ServiceProviderChannelModel? item;

        if (command is UpdateServiceProviderChannelCommand updateCommand)
        {
            item = _db.ServiceProviderChannels.Find(updateCommand.Id);
            if (item == null) throw new ItemNotFoundException();
        }
        else
        {
            item = new ServiceProviderChannelModel();
            _db.ServiceProviderChannels.Add(item);
        }

        item.NameAr = command.NameAr.Trim();
        item.NameEn = HelperFunctions.Default(command.NameEn?.Trim(), command.NameAr);

        _db.SaveChanges();

        return Task.FromResult(
            _db
                .ServiceProviderChannels
                .AsExpandable()
                .Select(ServiceProviderChannelGetDto.Mapper(HelperFunctions.GetLanguageCode()))
                .First(x => x.Id.Equals(item.Id))
        );
    }
}
