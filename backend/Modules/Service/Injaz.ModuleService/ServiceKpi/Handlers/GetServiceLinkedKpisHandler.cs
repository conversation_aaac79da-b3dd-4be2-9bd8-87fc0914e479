using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.Kpi;
using Injaz.Core.Dtos.Misc;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Services;
using Injaz.ModuleService.Core.ServiceKpi.Queries;
using MediatR;

namespace Injaz.ModuleService.ServiceKpi.Handlers;

public class GetServiceLinkedKpisHandler :
    IRequestHandler<GetServiceLinkedKpiListQuery, TableResultDto<KpiWithResultDto>>
{
    private readonly DbContext _db;
    private readonly AppSettingService _appSettingService;

    public GetServiceLinkedKpisHandler(
        DbContext db,
        AppSettingService appSettingService
    )
    {
        _db = db;
        _appSettingService = appSettingService;
    }

    public Task<TableResultDto<KpiWithResultDto>> Handle(
        GetServiceLinkedKpiListQuery query,
        CancellationToken cancellationToken
    )
    {
        query.Keyword = query.Keyword?.Trim().ToLower() ?? "";

        var items = _db
            .ServiceKpiLinks
            .AsExpandable()
            .Where(x => x.ServiceId.Equals(query.Id));

        var count = items.Count();

        items = items.Where(x =>
            x.Kpi.NameAr.ToLower().Contains(query.Keyword) ||
            x.Kpi.NameEn.ToLower().Contains(query.Keyword) ||
            x.Kpi.Code.ToLower().Equals(query.Keyword)
        );

        var canAchievedBeNegative = _appSettingService.Get().KpiSetting.CanResultAchievementTakeNegativeValues;

        return Task.FromResult(
            new TableResultDto<KpiWithResultDto>
            {
                Items = items
                    .Select(x => x.Kpi)
                    .Where(x => x.Status == Kpi.StatusActive)
                    .Select(
                        KpiWithResultDto.Mapper(HelperFunctions.GetLanguageCode(),
                            canAchievedBeNegative,
                            query.ResultFromYear ?? -1,
                            query.ResultToYear ?? -1
                        )
                    )
                    .OrderBy(x => x.Name)
                    .Skip(query.PageNumber * query.PageSize)
                    .Take(query.PageSize),
                Count = count,
                FilteredCount = items.Count()
            }
        );
    }
}
