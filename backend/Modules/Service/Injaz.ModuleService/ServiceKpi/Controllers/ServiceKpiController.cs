using Injaz.Core.Permission;
using Injaz.Core.Resources.Shared;
using Injaz.ModuleService.Core.ServiceKpi.Commands;
using Injaz.ModuleService.Core.ServiceKpi.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using MNMWebApp.Controllers;

namespace Injaz.ModuleService.ServiceKpi.Controllers;

[Route("/service/kpi/{id:guid}")]
public class ServiceKpiController : Controller
{
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly IMediator _mediator;

    public ServiceKpiController(
        IStringLocalizer<SharedResource> localizer,
        IMediator mediator
    )
    {
        _localizer = localizer;
        _mediator = mediator;
    }

    [HttpGet]
    public async Task<IActionResult> ListLinkedKpis(
        Guid id,
        string keyword,
        int? resultFromYear,
        int? resultToYear,
        int pageNumber = 0,
        int pageSize = 20
    )
    {
        return this.GetResponseObject(extra: await _mediator.Send(new GetServiceLinkedKpiListQuery()
        {
            Id = id,
            Keyword = keyword,
            ResultFromYear = resultFromYear,
            ResultToYear = resultToYear,
            PageNumber = pageNumber,
            PageSize = pageSize
        }));
    }

    [HttpGet("unlinked")]
    public async Task<IActionResult> ListUnlinkedKpis(
        Guid id,
        string keyword,
        string number,
        int pageNumber = 0,
        int pageSize = 20
    )
    {
        return this.GetResponseObject(extra: await _mediator.Send(new GetServiceUnlinkedKpiListQuery
        {
            Id = id,
            Keyword = keyword,
            Number = number,
            PageNumber = pageNumber,
            PageSize = pageSize
        }));
    }

    [HttpPost]
    [Authorize(Policy = PermissionNameList.Service)]
    public async Task<IActionResult> LinkWithKpi(Guid id, Guid kpiId)
    {
        await _mediator.Send(new LinkServiceWithKpiCommand
        {
            Id = id,
            KpiId = kpiId
        });

        return this.GetResponseObject(messages: new string[] { _localizer["operation_successful"] });
    }

    [HttpDelete]
    [Authorize(Policy = PermissionNameList.Service)]
    public async Task<IActionResult> UnlinkFromKpi(Guid id, Guid kpiId)
    {
        await _mediator.Send(new UnlinkServiceFromKpiCommand
        {
            Id = id,
            KpiId = kpiId
        });

        return this.GetResponseObject(messages: new string[] { _localizer["operation_successful"] });
    }
}
