using LinqKit;
using Injaz.Core;
using Injaz.ModuleKpi.Core.Kpi.Queries;
using MediatR;
using KpiModel = Injaz.Core.Models.DomainClasses.App.Kpi;

namespace Injaz.ModuleKpi.Kpi.Handlers;

public class GetAggregatedYearListHandler : IRequestHandler<GetKpiAggregatedYearListQuery, IEnumerable<int>>
{
    private readonly DbContext _db;

    public GetAggregatedYearListHandler(
        DbContext db
    )
    {
        _db = db;
    }

    public Task<IEnumerable<int>> Handle(GetKpiAggregatedYearListQuery request, CancellationToken cancellationToken)
    {
        var isInvolvedWithKpiExpression = HelperExpression
            .IsInvolvedWithKpi(request.CurrentUserId, _db.Departments, true)
            .Or(x => request.CurrentUserHasFullAccessPermission);

        var kpis = _db
            .Kpis
            .AsExpandable()
            .Where(x => x.Status.Equals(KpiModel.StatusActive))
            .Where(x => isInvolvedWithKpiExpression.Invoke(x));

        return Task.FromResult(kpis
            .Where(x => x.Id == request.Id)
            .SelectMany(x => x.Results)
            .Where(x => request.DepartmentIds == null || request.DepartmentIds.Length == 0 ||
                        request.DepartmentIds.Contains(x.DepartmentId))
            .Select(x => x.Year)
            .Distinct()
            .OrderBy(x => x)
            .AsEnumerable()
        );
    }
}
