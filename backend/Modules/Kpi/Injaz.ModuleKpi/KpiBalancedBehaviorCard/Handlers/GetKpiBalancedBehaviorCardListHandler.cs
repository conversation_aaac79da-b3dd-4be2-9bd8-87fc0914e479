using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.KpiBalancedBehaviorCard;
using Injaz.Core.Dtos.Misc;
using Injaz.ModuleKpi.Core.KpiBalancedBehaviorCard.Queries;
using MediatR;

namespace Injaz.ModuleKpi.KpiBalancedBehaviorCard.Handlers;

public class GetKpiBalancedBehaviorCardListHandler :
    IRequestHandler<GetKpiBalancedBehaviorCardListQuery, TableResultDto<KpiBalancedBehaviorCardListDto>>
{
    private readonly DbContext _db;

    public GetKpiBalancedBehaviorCardListHandler(DbContext db) => _db = db;

    public Task<TableResultDto<KpiBalancedBehaviorCardListDto>> Handle(
        GetKpiBalancedBehaviorCardListQuery query,
        CancellationToken cancellationToken
    )

    {
        query.Keyword = query.Keyword?.ToLower().Trim() ?? "";

        var items = _db
            .KpiBalancedBehaviorCards
            .AsExpandable()
            .Where(x => x.NameAr.Contains(query.Keyword) || x.NameEn.ToLower().Contains(query.Keyword));

        var lang = HelperFunctions.GetLanguageCode();

        return Task.FromResult(
            new TableResultDto<KpiBalancedBehaviorCardListDto>
            {
                Items = items
                    .Select(KpiBalancedBehaviorCardListDto.Mapper(lang))
                    .OrderBy(x => x.Name)
                    .Skip(query.PageSize * query.PageNumber)
                    .Take(query.PageSize)
                    .ToList(),
                Count = _db.KpiBalancedBehaviorCards.Count(),
                FilteredCount = items.Count()
            }
        );
    }
}
