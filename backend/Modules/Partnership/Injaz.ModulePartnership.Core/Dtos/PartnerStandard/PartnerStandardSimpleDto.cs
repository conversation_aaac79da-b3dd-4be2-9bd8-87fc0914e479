using System.Linq.Expressions;
using Injaz.Core.Constants;

namespace Injaz.ModulePartnership.Core.Dtos.PartnerStandard;

public class PartnerStandardSimpleDto
{
    public static Expression<Func<Injaz.Core.Models.DomainClasses.App.PartnerModel.PartnerStandard,
        PartnerStandardSimpleDto>> Mapper(string lang)
    {
        return item => new PartnerStandardSimpleDto
        {
            Id = item.Id,
            Name = lang == SupportedCultures.LanguageArabic ? item.NameAr : item.NameEn,
        };
    }

    public Guid Id { get; set; }
    public string Name { get; set; }
}
