using System.Linq.Expressions;
using LinqKit;
using Injaz.ModulePartnership.Core.Dtos.PartnershipActivityCommunicationTool;
using Injaz.ModulePartnership.Core.Dtos.PartnershipActivityPeriod;
using PartnershipActivityModel = Injaz.Core.Models.DomainClasses.App.PartnershipModel.PartnershipActivity;

namespace Injaz.ModulePartnership.Core.Dtos.PartnershipActivity;

public class PartnershipActivityEditDto
{
    public static Expression<Func<PartnershipActivityModel, PartnershipActivityEditDto>> Mapper()
    {
        var partnershipActivityCommunicationToolExpression = PartnershipActivityCommunicationToolEditDto.Mapper();
        var partnershipActivityPeriodExpression = PartnershipActivityPeriodDto.Mapper();

        return item => new PartnershipActivityEditDto
        {
            Id = item.Id,
            Purpose = item.Purpose,
            CommunicationTool = partnershipActivityCommunicationToolExpression.Invoke(item.CommunicationTool),
            Periods = item.Periods.Select(x => partnershipActivityPeriodExpression.Invoke(x)).ToList(),
            Year = item.Year
        };
    }

    public Guid Id { get; set; }

    public string Purpose { get; set; }

    public int Year { get; set; }
    public PartnershipActivityCommunicationToolEditDto CommunicationTool { get; set; }

    public IEnumerable<PartnershipActivityPeriodDto> Periods { get; set; }
}
