using Injaz.Core.Permission;
using Injaz.ModulePartnership.Core.Commands.PartnershipPartnerEvaluation;
using Injaz.ModulePartnership.Core.Queries.PartnershipPartnerEvaluation;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MNMWebApp.Binders;
using MNMWebApp.Controllers;

namespace Injaz.ModulePartnership.Controllers;

[Route("/partnership/{id:guid}/partner-evaluation")]
[Authorize(Policy = PermissionNameList.PartnershipWrite)]
public class PartnershipPartnerEvaluationController : Controller
{
    private readonly IMediator _mediator;

    public PartnershipPartnerEvaluationController(IMediator mediator)
    {
        _mediator = mediator;
    }

    [HttpGet]
    public async Task<IActionResult> GetPartnershipPartnerEvaluations(Guid id)
    {
        return this.GetResponseObject(extra: await _mediator.Send(
            new GetPartnershipPartnerEvaluationQuery() { PartnershipContractId = id })
        );
    }

    [HttpPut]
    public async Task<IActionResult> UpdatePartnershipPartnerEvaluations(
        Guid id,
        [BindBodySingleJson] UpdatePartnershipPartnerEvaluationCommand data
    )
    {
        data.PartnershipContractId = id;
        await _mediator.Send(data);
        return this.GetResponseObject(messages: new[] { "data_saved_successfully" });
    }
}
