using System.ComponentModel.DataAnnotations;
using Injaz.Core.Dtos.Principle;
using MediatR;

namespace Injaz.ModuleTournament.Core.Commands;

public class CreatePrincipleCommand : IRequest<PrincipleGetDto>
{
    [Display(Name = "name_in_arabic")]
    [Required(ErrorMessage = "0_is_required")]
    public string NameAr { get; set; }

    [Display(Name = "name_in_english")] public string NameEn { get; set; }

    [Display(Name = "description_in_arabic")]
    public string DescriptionAr { get; set; }

    [Display(Name = "description_in_english")]
    public string DescriptionEn { get; set; }

    [Display(Name = "weight")]
    [Required(ErrorMessage = "0_is_required")]
    [Range(0, 1, ErrorMessage = "0_can_be_between_1_and_2")]
    public double? Weight { get; set; }

    // Only used during creation
    public Guid? StandardId { get; set; }
}
