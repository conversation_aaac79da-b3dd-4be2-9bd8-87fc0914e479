using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.Standard;
using Injaz.ModuleTournament.Core.Queries;
using MediatR;

namespace Injaz.ModuleTournament.Handlers;

public class GetStandardTeamByIdHandler : IRequestHandler<GetStandardTeamByIdQuery, StandardTeamGetDto>
{
    private readonly IDbContext _db;

    public GetStandardTeamByIdHandler(IDbContext db) => _db = db;

    public Task<StandardTeamGetDto> Handle(
        GetStandardTeamByIdQuery query,
        CancellationToken cancellationToken
    )
    {
        return Task.FromResult(
            _db.Standards
                .AsExpandable()
                .Select(
                    StandardTeamGetDto.Mapper(
                        HelperFunctions.GetLanguageCode()
                    )
                )
                .FirstOrDefault(p => p.Id.Equals(query.StandardId))
        );
    }
}
