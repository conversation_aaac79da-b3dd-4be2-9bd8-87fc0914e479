using Injaz.Core.Extensions;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Permission;
using Injaz.Core.Resources.Shared;
using Injaz.ModuleTournament.Core.Commands;
using Injaz.ModuleTournament.Core.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using MNMWebApp.Binders;
using MNMWebApp.Controllers;

namespace Injaz.ModuleTournament.Controllers;

public class StandardTaskCrudController : Controller
{
    private readonly IMediator _mediator;
    private readonly IStringLocalizer<SharedResource> _localizer;
    private readonly UserManager<User> _userManager;

    public StandardTaskCrudController(
        IMediator mediator,
        IStringLocalizer<SharedResource> localizer,
        UserManager<User> userManager
    )
    {
        _mediator = mediator;
        _localizer = localizer;
        _userManager = userManager;
    }

    [HttpGet]
    [Route("/standard/task")]
    public async Task<IActionResult> List(
        string keyword = "",
        Guid[] tournamentIds = null,
        Guid[] pillarIds = null,
        Guid[] standardIds = null,
        DateTime? from = null,
        DateTime? to = null,
        int pageNumber = 0,
        int pageSize = 20
    )
    {
        return this.GetResponseObject(extra: await _mediator.Send(new GetStandardTaskListQuery()
        {
            CurrentUserId = Guid.Parse(_userManager.GetUserId(User)),
            CurrentUserHasTournamentPermission = await this.EnsureUserHasPermission(PermissionNameList.Tournament),
            Keyword = keyword,
            From = from,
            To = to,
            TournamentIds = tournamentIds,
            PillarIds = pillarIds,
            StandardIds = standardIds,
            PageNumber = pageNumber,
            PageSize = pageSize
        }));
    }

    [HttpGet]
    [Route("/standard/task/{id:guid}")]
    public async Task<IActionResult> Get(Guid id, bool forEdit = false)
    {
        var item = forEdit
            ? await _mediator.Send(new GetStandardTaskForEditByIdQuery { Id = id }) as object
            : await _mediator.Send(new GetStandardTaskByIdQuery
            {
                Id = id,
                CurrentUserId = Guid.Parse(_userManager.GetUserId(User)),
                CurrentUserHasTournamentPermission = await this.EnsureUserHasPermission(PermissionNameList.Tournament),
            });

        return this.GetResponseObject(extra: item);
    }

    [HttpPost]
    [Authorize(Policy = PermissionNameList.Tournament)]
    [Route("/standard/{standardId:guid}/task")]
    public async Task<IActionResult> Create([BindBodySingleJson] CreateStandardTaskCommand task, Guid standardId)
    {
        task.StandardId = standardId;
        task.CurrentUserId = Guid.Parse(_userManager.GetUserId(User));
        task.CurrentUserHasTournamentPermission = true;

        var item = await _mediator.Send(task);

        return this.GetResponseObject(extra: item);
    }

    [HttpPut]
    [Authorize(Policy = PermissionNameList.Tournament)]
    [Route("/standard/task")]
    public async Task<IActionResult> Update([BindBodySingleJson] UpdateStandardTaskCommand task)
    {
        task.CurrentUserId = Guid.Parse(_userManager.GetUserId(User));
        task.CurrentUserHasTournamentPermission = true;

        var item = await _mediator.Send(task);

        return this.GetResponseObject(extra: item);
    }

    [HttpDelete]
    [Authorize(Policy = PermissionNameList.Tournament)]
    [Route("/standard/task/{id:guid}")]
    public async Task<IActionResult> Delete(Guid id)
    {
        await _mediator.Send(new DeleteStandardTaskCommand { Id = id });

        return this.GetResponseObject(
            messages: new string[] { _localizer["operation_successful"] }
        );
    }
}
