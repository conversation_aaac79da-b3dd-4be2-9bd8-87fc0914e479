using Injaz.Core.Permission;
using Injaz.Core.Resources.Shared;
using Injaz.ModuleTournament.Core.Commands;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using MNMWebApp.Controllers;

namespace Injaz.ModuleTournament.Controllers;

[Authorize(Policy = PermissionNameList.Tournament)]
public class StandardUserController : Controller
{
    private readonly IMediator _mediator;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public StandardUserController(
        IMediator mediator,
        IStringLocalizer<SharedResource> localizer
    )
    {
        _mediator = mediator;
        _localizer = localizer;
    }

    [HttpPost]
    [Route("/standard/{standardId:guid}/user")]
    public async Task<IActionResult> Add(Guid standardId, Guid[] userIds)
    {
        await _mediator.Send(new AddUserListToStandardCommand
        {
            Id = standardId,
            UserIds = userIds
        });

        return this.GetResponseObject(
            messages: new string[] { _localizer["operation_successful"] }
        );
    }

    [HttpDelete]
    [Route("/standard/{standardId:guid}/user/{userId:guid}")]
    public async Task<IActionResult> Remove(Guid standardId, Guid userId)
    {
        await _mediator.Send(new RemoveUserFromStandardCommand
        {
            Id = standardId,
            UserId = userId
        });

        return this.GetResponseObject(
            messages: new string[] { _localizer["operation_successful"] }
        );
    }

    [HttpPut]
    [Route("/standard/{standardId:guid}/user/{userId:guid}")]
    public async Task<IActionResult> UpdateRoles(Guid standardId, Guid userId, string[] roles)
    {
        await _mediator.Send(new UpdateStandardUserRoleCommand
        {
            Id = standardId,
            UserId = userId,
            Roles = roles
        });

        return this.GetResponseObject(
            messages: new string[] { _localizer["operation_successful"] }
        );
    }
}
