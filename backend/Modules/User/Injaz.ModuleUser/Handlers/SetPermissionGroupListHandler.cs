using Injaz.Core.Exceptions;
using Injaz.Core.Resources.Shared;
using Injaz.Core.Services;
using Injaz.ModuleUser.Core.Commands;
using MediatR;
using Microsoft.Extensions.Localization;

namespace Injaz.ModuleUser.Handlers;

public class SetPermissionGroupListHandler : IRequestHandler<SetUserPermissionGroupListCommand>
{
    private readonly AdminUserService _adminUserService;
    private readonly PermissionService _permissionService;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public SetPermissionGroupListHandler(
        AdminUserService adminUserService,
        PermissionService permissionService,
        IStringLocalizer<SharedResource> localizer)
    {
        _adminUserService = adminUserService;
        _permissionService = permissionService;
        _localizer = localizer;
    }

    public async Task<Unit> Handle(SetUserPermissionGroupListCommand request, CancellationToken cancellationToken)
    {
        // Ensure that the user is not admin.
        if (request.Id == _adminUserService.GetId())
        {
            throw new GenericException
            {
                Messages = new string[] { _localizer["cannot_set_permissions_for_main_admin_user"] }
            };
        }

        await _permissionService.SetPermissionGroupLinks(request.Id,
            request.PermissionGroupIdList
        );

        return default;
    }
}