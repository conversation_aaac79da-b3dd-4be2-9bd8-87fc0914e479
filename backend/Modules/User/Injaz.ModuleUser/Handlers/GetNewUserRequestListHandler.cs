using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.Misc;
using Injaz.Core.Dtos.NewUserRequest;
using Injaz.Core.Flow.Services;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Services;
using Injaz.ModuleUser.Core.Commands;
using Injaz.ModuleUser.Core.Queries;
using MediatR;

namespace Injaz.ModuleUser.Handlers;

public class
    GetNewUserRequestListHandler : IRequestHandler<GetNewUserRequestListQuery, TableResultDto<NewUserRequestListDto>>
{
    private readonly DbContext _db;
    private readonly CurrentUserService _currentUserService;
    private readonly FlowService<NewUserRequest> _flowService;

    public GetNewUserRequestListHandler(DbContext db,
        CurrentUserService currentUserService,
        FlowService<NewUserRequest> flowService)
    {
        _db = db;
        _currentUserService = currentUserService;
        _flowService = flowService;
    }

    public Task<TableResultDto<NewUserRequestListDto>> Handle(GetNewUserRequestListQuery query,
        CancellationToken cancellationToken)
    {
        query.Keyword = query.Keyword?.ToLower().Trim() ?? "";

        var items = _db
            .NewUserRequests
            .AsExpandable()
            .Where(x => x.NameAr.Contains(query.Keyword) || x.NameEn.ToLower().Contains(query.Keyword))
            .Where(x => query.FlowStates == null || query.FlowStates.Length == 0 ||
                        query.FlowStates.Contains(x.FlowState));

        return Task.FromResult(new TableResultDto<NewUserRequestListDto>
        {
            Items = items
                .OrderByDescending(x => x.CreationTime)
                .Skip(query.PageSize * query.PageNumber)
                .Take(query.PageSize)
                .Select(NewUserRequestListDto.Mapper(
                    HelperFunctions.GetLanguageCode(),
                    _currentUserService.GetId(),
                    _flowService.GenerateActionAvailability()))
                .ToList(),
            Count = _db.NewUserRequests.Count(),
            FilteredCount = items.Count()
        });
    }
}