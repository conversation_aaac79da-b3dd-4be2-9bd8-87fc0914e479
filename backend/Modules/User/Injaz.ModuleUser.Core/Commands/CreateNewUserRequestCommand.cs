using System.ComponentModel.DataAnnotations;
using Injaz.Core.Dtos.Department;
using Injaz.Core.Dtos.User;
using Injaz.Core.ValidationAttributes;
using MediatR;

namespace Injaz.ModuleUser.Core.Commands;

public class CreateNewUserRequestCommand : IRequest
{
    [Display(Name = "name_in_arabic")]
    [Required(ErrorMessage = "0_is_required")]
    public string NameAr { get; set; }

    [Display(Name = "name_in_english")]
    [Required(ErrorMessage = "0_is_required")]
    public string NameEn { get; set; }

    [Display(Name = "gender")]
    [RegularExpression("^(?:male|female)$", ErrorMessage = "0_is_invalid")]
    [Required(ErrorMessage = "0_is_required")]
    public string Gender { get; set; }

    [Display(Name = "employee_number")]
    [Required(ErrorMessage = "0_is_required")]
    public string EmployeeNumber { get; set; }

    [Display(Name = "password")]
    [Required(ErrorMessage = "0_is_required")]
    public string Password { get; set; }

    [Display(Name = "email")]
    [EmailAddress(ErrorMessage = "0_is_invalid")]
    [Required(ErrorMessage = "0_is_required")]
    public string Email { get; set; }

    [Required(ErrorMessage = "0_is_required")]
    [Display(Name = "rank")]
    public string Rank { get; set; }

    [Display(Name = "requested_permissions")]
    [Required(ErrorMessage = "0_is_required")]
    public string RequestedPermissions { get; set; }

    [Display(Name = "department")]
    [Required(ErrorMessage = "0_is_required")]
    public DepartmentSimpleDto Department { get; set; }
}
