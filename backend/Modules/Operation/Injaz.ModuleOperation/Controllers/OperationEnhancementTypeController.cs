using Injaz.Core;
using Injaz.Core.Constants;
using Injaz.Core.Dtos.OperationEnhancementType;
using Injaz.Core.Models;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Permission;
using Injaz.Core.Resources.Shared;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using MNMWebApp.Binders;
using MNMWebApp.Controllers;

namespace Injaz.ModuleOperation.Controllers;

[Authorize(Policy = PermissionNameList.OperationEnhancementType)]
public class OperationEnhancementTypeController : Controller
{
    private readonly AppDataContext _appDataContext;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public OperationEnhancementTypeController(
        AppDataContext appDataContext,
        IStringLocalizer<SharedResource> localizer
    )
    {
        _appDataContext = appDataContext;
        _localizer = localizer;
    }

    [HttpGet]
    [Route("/operation-enhancement-type")]
    public IActionResult List(
        string keyword = "",
        int pageNumber = 0,
        int pageSize = 20
    )
    {
        keyword = keyword?.ToLower().Trim() ?? "";

        var items = _appDataContext
            .OperationEnhancementTypes
            .Where(x => x.NameAr.Contains(keyword) || x.NameEn.ToLower().Contains(keyword));

        var lang = HelperFunctions.GetLanguageCode();

        return this.GetResponseObject(extra: new
        {
            Items = items
                .OrderBy(x => lang == SupportedCultures.LanguageArabic ? x.NameAr : x.NameEn)
                .Skip(pageSize * pageNumber)
                .Take(pageSize)
                .Select(OperationEnhancementTypeGetDto.Mapper(lang))
                .ToList(),
            Count = _appDataContext.OperationEnhancementTypes.Count(),
            FilteredCount = items.Count()
        });
    }

    [HttpGet]
    [Route("/operation-enhancement-type/{id}")]
    public IActionResult Get(
        Guid id,
        bool forEdit = false
    )
    {
        var item = forEdit
            ? _appDataContext.OperationEnhancementTypes
                .Select(OperationEnhancementTypeEditDto.Mapper())
                .FirstOrDefault(x => x.Id.Equals(id)) as object
            : _appDataContext.OperationEnhancementTypes
                .Select(OperationEnhancementTypeGetDto.Mapper(HelperFunctions.GetLanguageCode()))
                .FirstOrDefault(x => x.Id.Equals(id));

        if (item == null)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 404,
                messages: new string[] { _localizer["item_not_found"] }
            );
        }

        return this.GetResponseObject(extra: item);
    }

    [HttpPost]
    [Route("/operation-enhancement-type")]
    public IActionResult Create(
        [BindBodySingleJson] OperationEnhancementTypeCreateDto operationEnhancementType
    )
    {
        // Ensure no errors.
        if (!ModelState.IsValid)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: ModelState.SelectMany(x => x.Value!.Errors.Select(y => y.ErrorMessage)).ToArray()
            );
        }

        // Create and add the new item.
        var item = new OperationEnhancementType
            { NameAr = operationEnhancementType.NameAr.Trim(), NameEn = operationEnhancementType.NameEn.Trim() };
        _appDataContext.OperationEnhancementTypes.Add(item);
        _appDataContext.SaveChanges();


        // Return the newly created item using dtos.
        return this.GetResponseObject(
            extra: _appDataContext
                .OperationEnhancementTypes
                .Select(OperationEnhancementTypeGetDto.Mapper(HelperFunctions.GetLanguageCode()))
                .First(x => x.Id.Equals(item.Id))
        );
    }

    [HttpPut]
    [Route("/operation-enhancement-type")]
    public IActionResult Update(
        [BindBodySingleJson] OperationEnhancementTypeEditDto operationEnhancementType
    )
    {
        // Ensure no errors.
        if (!ModelState.IsValid)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: ModelState.SelectMany(x => x.Value!.Errors.Select(y => y.ErrorMessage)).ToArray()
            );
        }

        // Get item.
        var item = _appDataContext.OperationEnhancementTypes.Find(operationEnhancementType.Id);

        // Ensure the item exists.
        if (item == null)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 404,
                messages: new string[] { _localizer["item_not_found"] }
            );
        }

        // Update the item.
        item.NameAr = operationEnhancementType.NameAr.Trim();
        item.NameEn = operationEnhancementType.NameEn.Trim();
        _appDataContext.SaveChanges();

        // Return the newly created item using dtos.
        return this.GetResponseObject(
            extra: _appDataContext
                .OperationEnhancementTypes
                .Select(OperationEnhancementTypeGetDto.Mapper(HelperFunctions.GetLanguageCode()))
                .First(x => x.Id.Equals(item.Id))
        );
    }

    [HttpDelete]
    [Route("/operation-enhancement-type/{id}")]
    public IActionResult Delete(
        Guid id
    )
    {
        // Get item.
        var data = _appDataContext
            .OperationEnhancementTypes
            .Where(x => x.Id == id)
            .Select(x => new
            {
                Item = x,
                IsLinkedToOtherResources = x.OperationEnhancements.Any()
            })
            .SingleOrDefault();

        // Ensure the item exists.
        if (data == null)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 404,
                messages: new string[] { _localizer["item_not_found"] }
            );
        }

        // Ensure that the enhancement type is not linked
        // to other resources.
        if (data.IsLinkedToOtherResources)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 404,
                messages: new string[]
                    { _localizer["cannot_delete_enhancement_type_since_it_is_connected_to_other_resources"] }
            );
        }


        // Remove the item.
        _appDataContext.OperationEnhancementTypes.Remove(data.Item);

        _appDataContext.SaveChanges();

        return this.GetResponseObject(
            messages: new string[] { _localizer["item_removed_successfully"] }
        );
    }
}
