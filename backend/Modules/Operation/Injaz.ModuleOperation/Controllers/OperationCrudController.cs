using LinqKit;
using Injaz.Core;
using Injaz.Core.Dtos.Operation;
using Injaz.Core.Dtos.StrategicGoal;
using Injaz.Core.Dtos.SuccessFactor;
using Injaz.Core.Extensions;
using Injaz.Core.Helpers;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Permission;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.EntityFrameworkCore;
using MNMWebApp.Binders;
using MNMWebApp.Controllers;

namespace Injaz.ModuleOperation.Controllers;

[Authorize]
public partial class OperationController
{
    [HttpGet]
    [Route("/operation")]
    [Authorize(Policy = PermissionNameList.OperationRead)]
    public async Task<IActionResult> List(
        string keyword = "",
        string version = "",
        string code = "",
        string level = "",
        Guid[]? strategicGoalIds = null,
        Guid[]? departmentIds = null,
        bool includeChildDepartments = false,
        int pageNumber = 0,
        int pageSize = 20
    )
    {
        keyword = keyword?.ToLower().Trim() ?? "";
        version = version?.ToLower().Trim() ?? "";
        code = code?.ToLower().Trim() ?? "";
        level = level?.ToLower().Trim() ?? "";

        int.TryParse(level, out var opLevel);

        var currentUserId = new Guid(_userManager.GetUserId(User));

        var hasOperationPermission = await this.EnsureUserHasPermission(PermissionNameList.Operation);

        var isInvolvedWithDepartment = HelperExpression
            .IsInvolvedWithDepartment(currentUserId, _appDataContext.Departments, true)
            .Or(x => hasOperationPermission);

        var items = _appDataContext
            .Operations
            .AsExpandable()
            .Where(x => isInvolvedWithDepartment.Invoke(x.OwnerDepartment));

        var lang = HelperFunctions.GetLanguageCode();

        if (!string.IsNullOrEmpty(keyword))
            items = items.Where(x => x.NameAr.Contains(keyword) || x.NameEn.ToLower().Contains(keyword));

        if (!string.IsNullOrEmpty(version))
            items = items.Where(x => x.Version.Contains(version));

        if (!string.IsNullOrEmpty(code))
            items = items.Where(x => x.Code == code);

        if (departmentIds is { Length: > 0 })
        {
            items = items
                .Where(x => x.OwnerDepartmentId != null)
                .Where(x => _appDataContext.Departments
                    .Where(y => departmentIds.Contains(y.Id))
                    .Any(y =>
                        includeChildDepartments
                            ? x.OwnerDepartment.HierarchyCode.StartsWith(y.HierarchyCode)
                            : x.OwnerDepartment.HierarchyCode == y.HierarchyCode
                    )
                );
        }

        if (strategicGoalIds is { Length: > 0 })
        {
            items = items.Where(x => x.GoalLinks.Any(y => strategicGoalIds.Contains(y.GoalId)));
        }

        if (opLevel > 0) items = items.Where(x => x.Level == opLevel);

        return this.GetResponseObject(extra: new
        {
            Items = items
                .OrderBy(x => x.Code)
                .Skip(pageSize * pageNumber)
                .Take(pageSize)
                .Select(OperationListDto.Mapper(lang))
                .ToList(),
            Count = _appDataContext.Operations.Count(),
            FilteredCount = items.Count()
        });
    }

    [HttpGet]
    [Route("/operation/by-parent")]
    public async Task<IActionResult> ListByParent(Guid? parentId)
    {
        var currentUserId = new Guid(_userManager.GetUserId(User));

        var hasOperationPermission = await this.EnsureUserHasPermission(PermissionNameList.Operation);

        var isInvolvedWithDepartment = HelperExpression
            .IsInvolvedWithDepartment(currentUserId, _appDataContext.Departments, true)
            .Or(x => hasOperationPermission);

        return this.GetResponseObject(extra: _appDataContext
            .Operations
            .AsExpandable()
            .Where(x => isInvolvedWithDepartment.Invoke(x.OwnerDepartment))
            .Where(x => x.ParentId == parentId)
            .OrderBy(x => x.Code)
            .Select(OperationListDto.Mapper(HelperFunctions.GetLanguageCode()))
        );
    }

    // In OperationController.cs
    [HttpGet]
    [Route("/operation/{id:guid}/detailed")]
    public IActionResult GetDetailed(Guid id)
    {
        var operation = _appDataContext.Operations
            .Include(x => x.OwnerDepartment)
            .Include(x => x.MainOperationOwner)
            .Include(x => x.Parent)
            .Include(x => x.Children)
            .Include(x => x.GoalLinks).ThenInclude(x => x.StrategicGoal)
            .Include(x => x.SuccessFactorOperationLinks).ThenInclude(x => x.SuccessFactor)
            .Include(x => x.PartnerLinks).ThenInclude(x => x.Partner)
            .Include(x => x.PolicyLinks).ThenInclude(x => x.Policy)
            .Include(x => x.SpecificationLinks).ThenInclude(x => x.Specification)
            .Include(x => x.RuleAndRegulationLinks).ThenInclude(x => x.OperationRuleAndRegulation)
            .Include(x => x.ServiceLinks).ThenInclude(x => x.Service)
            .Include(x => x.KpiLinks).ThenInclude(x => x.Kpi)
            .Include(x => x.Enhancements).ThenInclude(x => x.Type)
            .Include(x => x.Procedures).ThenInclude(x => x.Steps)
            .Include(x => x.FormFileLinks).ThenInclude(x => x.LibraryFile)
            .Include(x => x.Executors)
            .FirstOrDefault(x => x.Id == id);

        if (operation == null)
        {
            return NotFound();
        }

        // Get parent hierarchy in a single query
        var parentCodes = new List<string>();
        var currentCode = operation.Code;
        while (currentCode.Contains('-'))
        {
            currentCode = currentCode.Substring(0, currentCode.LastIndexOf('-'));
            parentCodes.Add(currentCode);
        }

        var parents = _appDataContext.Operations
            .AsExpandable()
            .Where(x => parentCodes.Contains(x.Code))
            .OrderBy(x => x.Level)
            .Select(OperationSimpleDto.Mapper(HelperFunctions.GetLanguageCode()))
            .ToList();

        var result = new
        {
            Operation = operation, // Map to your DTO here
            Parents = parents
        };

        return Ok(result);
    }

    [HttpGet]
    [Route("/operation/{id:guid}")]
    [Authorize(Policy = PermissionNameList.OperationRead)]
    public IActionResult Get(
        Guid id,
        bool forEdit = false
    )
    {
        var canAchievedBeNegative = _appSettingService.Get().KpiSetting.CanResultAchievementTakeNegativeValues;

        var item = forEdit
            ? _appDataContext.Operations
                .AsExpandable()
                .Select(OperationEditDto.Mapper(HelperFunctions.GetLanguageCode()))
                .FirstOrDefault(x => x.Id.Equals(id)) as object
            : _appDataContext.Operations
                .AsExpandable()
                .Select(
                    OperationGetDto.Mapper(
                        _appDataContext.Operations.AsQueryable(),
                        HelperFunctions.GetLanguageCode(),
                        canAchievedBeNegative
                    )
                )
                .FirstOrDefault(x => x.Id.Equals(id));

        // ONLY FOR NORMAL GET
        // Check if the operation is not in the
        // first level, then load the success factors,
        // and goals of the greatest operation parent.
        if (item is OperationGetDto operation && operation.Level != 1)
        {
            var greatestParentCode = operation.Code?.Split('-')[0];

            var successFactorExpression = SuccessFactorDto.Mapper(HelperFunctions.GetLanguageCode());
            var goalExpression = StrategicGoalSimpleDto.Mapper(HelperFunctions.GetLanguageCode());

            var data = _appDataContext
                .Operations
                .AsExpandable()
                .Where(x => x.Code == greatestParentCode)
                .Select(x => new
                {
                    SuccessFactors = x.SuccessFactorOperationLinks
                        .Select(y => successFactorExpression.Invoke(y.SuccessFactor)).ToList(),
                    Goals = x.GoalLinks.Select(y => goalExpression.Invoke(y.StrategicGoal)).ToList(),
                })
                .FirstOrDefault();

            if (data != null)
            {
                operation.SuccessFactors = data.SuccessFactors;
                operation.StrategicGoals = data.Goals;
            }
        }

        if (item == null)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 404,
                messages: new string[] { _localizer["item_not_found"] }
            );
        }

        return this.GetResponseObject(extra: item);
    }

    [HttpPost]
    [Route("/operation")]
    [Authorize(Policy = PermissionNameList.OperationWrite)]
    public IActionResult Create(
        [BindBodySingleJson] OperationCreateDto operation
    )
    {
        return CreateOrUpdate(operation);
    }

    [HttpPut]
    [Route("/operation")]
    [Authorize(Policy = PermissionNameList.OperationWrite)]
    public IActionResult Update(
        [BindBodySingleJson] OperationEditDto operation
    )
    {
        return CreateOrUpdate(operation);
    }

    [HttpPut]
    [Route("/operation/special-level-detail")]
    [Authorize(Policy = PermissionNameList.OperationWrite)]
    public IActionResult UpdateSpecialLevelDetail(
        [BindBodySingleJson] OperationEditSpecialLevelDetail operation
    )
    {
        var item = _appDataContext.Operations.Find(operation?.Id);
        if (item == null)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 404,
                messages: new string[] { _localizer["item_not_found"] }
            );
        }

        item.Inputs = operation?.Inputs;
        item.InputType = operation?.InputType;
        item.Outputs = operation?.Outputs;
        item.OutputType = operation?.OutputType;
        item.TechnicalSolutions = operation?.TechnicalSolutions;
        item.MainFLowChartId = operation?.MainChartFlow?.Id;
        item.Types = operation?.Types?.ToArray() ?? new string[] { };
        item.PartnerLinks = operation?.Partners
            ?.Select(x => new OperationPartnerLink()
            {
                PartnerId = x.Id,
            }).ToList();

        item.ServiceLinks = operation
            ?.Services
            ?.Select(x => new OperationServiceLink() { ServiceId = x.Id })
            .ToList();

        item.Beneficiaries = operation?.Beneficiaries?.ToArray() ?? Array.Empty<string>();

        item.Executors = operation?
            .Executors?
            .Where(x => !string.IsNullOrEmpty(x.Type) && !string.IsNullOrEmpty(x.Name))
            .Select(x => new OperationExecutor
            {
                Type = x.Type,
                Name = x.Name
            }).ToList();

        item.FormFileLinks = operation?.FormFiles
            .Where(x => !string.IsNullOrEmpty(x.Type) && x.LibraryFile != null)
            .GroupBy(x => x.LibraryFile.Id)
            .Select(x => x.First())
            .Select(x => new OperationFormFileLink
            {
                Type = x.Type,
                FileId = x.LibraryFile.Id,
            }).ToList();

        item.RuleAndRegulationLinks = operation?.RulesAndRegulations
            .Select(x =>
                new OperationRuleAndRegulationLink
                {
                    RuleAndRegulationId = x.Id
                }).ToList();

        RemoveRelations(item.Id);
        _appDataContext.Entry(item).State = EntityState.Modified;
        _appDataContext.SaveChanges();

        return this.GetResponseObject(
            success: 1,
            messages: new string[] { _localizer["operation_successful"] }
        );
    }

    [HttpDelete]
    [Route("/operation/{id}")]
    [Authorize(Policy = PermissionNameList.OperationDelete)]
    public IActionResult Delete(Guid id)
    {
        // Get item.
        var data = _appDataContext.Operations
            .Where(x => x.Id == id)
            .Select(x => new
            {
                Item = x,
                ToBeNullified = new { x.Children },
                ToBeRemoved = new
                {
                    x.PlanLinks,
                    x.PlanTaskLinks,
                    x.KpiLinks,
                    x.PartnerLinks,
                    ProcedureKpiLinks = x.Procedures.SelectMany(y => y.KpiLinks),
                    x.BenchmarkLinks,
                    x.UserRequests,
                    x.RiskLinks
                }
            })
            .FirstOrDefault();

        // Ensure the item exists.
        if (data == null)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 404,
                messages: new string[] { _localizer["item_not_found"] }
            );
        }

        // Remove linked resources.
        _appDataContext.RemoveRange(data.ToBeRemoved.PlanLinks);
        _appDataContext.RemoveRange(data.ToBeRemoved.PlanTaskLinks);
        _appDataContext.RemoveRange(data.ToBeRemoved.KpiLinks);
        _appDataContext.RemoveRange(data.ToBeRemoved.PartnerLinks);
        _appDataContext.RemoveRange(data.ToBeRemoved.ProcedureKpiLinks);
        _appDataContext.RemoveRange(data.ToBeRemoved.BenchmarkLinks);
        _appDataContext.RemoveRange(data.ToBeRemoved.UserRequests);
        _appDataContext.RemoveRange(data.ToBeRemoved.RiskLinks);

        // nullify item id in resources.
        data.ToBeNullified.Children.ToList().ForEach(x => x.ParentId = null);

        // Remove the item.
        _appDataContext.Remove(data.Item);

        _appDataContext.SaveChanges();

        return this.GetResponseObject(
            messages: new string[] { _localizer["item_removed_successfully"] }
        );
    }

    [HttpGet]
    [Route("/operation/next-code")]
    [Authorize(Policy = PermissionNameList.OperationWrite)]
    public IActionResult GetNextCode(Guid? id, Guid? parentId)
    {
        if (id != null)
        {
            var operation = _appDataContext
                .Operations
                .Where(x => x.Id.Equals(id))
                .Select(x => new
                {
                    x.ParentId,
                    x.Code,
                    x.Number
                })
                .FirstOrDefault();

            if (operation == null)
            {
                return this.GetResponseObject(success: 0, statusCode: 404,
                    messages: new string[] { _localizer["item_not_found"] });
            }

            if (operation.ParentId == parentId)
            {
                return this.GetResponseObject(extra: new
                {
                    operation.Code,
                    operation.Number
                });
            }
        }

        if (parentId != null)
        {
            var data = _appDataContext
                .Operations
                .Where(x => x.Id.Equals(parentId))
                .Select(x => new
                {
                    x.Code,
                    x.Number,
                    NewNumber = x.Children.Any()
                        ? x.Children.OrderByDescending(y => y.Number).Select(y => new { y.Number }).First().Number +
                          1
                        : 1
                })
                .FirstOrDefault();

            if (data == null)
            {
                return this.GetResponseObject(success: 0, statusCode: 404,
                    messages: new string[] { _localizer["item_not_found"] });
            }

            return this.GetResponseObject(extra:
                new
                {
                    Code = $"{data.Code}-{data.NewNumber}",
                    Number = data.NewNumber
                });
        }

        var lastNumber = _appDataContext
            .Operations
            .OrderByDescending(x => x.Number)
            .Select(x => new { x.Number })
            .FirstOrDefault()?.Number;

        var newNumber = lastNumber == null ? 1 : lastNumber + 1;

        return this.GetResponseObject(extra: new
        {
            Number = newNumber,
            Code = $"{newNumber}"
        });
    }

    private IActionResult CreateOrUpdate(OperationCreateDto operation)
    {
        if (!Validate(operation, out var errorResult))
        {
            return errorResult;
        }

        if (operation.Level <= 1 && operation.StrategicGoals?.Count() < 1)
        {
            return this.GetResponseObject(
                success: 0,
                statusCode: 404,
                messages: new string[] { _localizer["you_must_select_the_strategic_goal"] }
            );
        }

        Operation item;
        if (operation is OperationEditDto existingOperation)
        {
            // Get item.
            item = _appDataContext.Operations.Find(existingOperation.Id);

            // Ensure the item exists.
            if (item == null)
            {
                return this.GetResponseObject(
                    success: 0,
                    statusCode: 404,
                    messages: new string[] { _localizer["item_not_found"] }
                );
            }

            RemoveRelations(item.Id);
        }
        else
        {
            item = new Operation();
        }

        var parentLevel = operation.ParentOperation == null
            ? null
            : _appDataContext
                .Operations
                .Where(x => x.Id.Equals(operation.ParentOperation.Id))
                .Select(x => new { x.Level }).FirstOrDefault()?.Level;

        // If the operation is at the forth level, add
        // in all additional fields.
        if (operation.ParentOperation != null)
        {
            if (parentLevel == null)
            {
                return this.GetResponseObject(success: 0, statusCode: 423,
                    messages: new string[] { _localizer["parent_not_found"] });
            }
        }

        Utils.MapCreateDtoToItem(operation, item, parentLevel);


        // Add the item if it is new.
        if (operation is not OperationEditDto)
        {
            _appDataContext.Operations.Add(item);
        }
        else
        {
            _appDataContext.Entry(item).State = EntityState.Modified;
        }

        _appDataContext.SaveChanges();

        var canAchievedBeNegative = _appSettingService.Get().KpiSetting.CanResultAchievementTakeNegativeValues;

        // Return the item using the appropriate dto.
        return this.GetResponseObject(
            extra: _appDataContext
                .Operations
                .AsExpandable()
                .Where(x => x.Id.Equals(item.Id))
                .Select(
                    OperationGetDto.Mapper(
                        _appDataContext.Operations.AsQueryable(),
                        HelperFunctions.GetLanguageCode(),
                        canAchievedBeNegative
                    )
                )
                .First()
        );
    }

    private bool Validate(OperationCreateDto operation, out IActionResult errorResult)
    {
        errorResult = null;

        // Ensure no errors.
        if (!ModelState.IsValid)
        {
            errorResult = this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: ModelState.SelectMany(x => x.Value!.Errors.Select(y => y.ErrorMessage)).ToArray()
            );
            return false;
        }

        // Ensure that the code is not
        // duplicated
        var existingId = operation is OperationEditDto operationEditDto ? (Guid?)operationEditDto.Id : null;
        if (_appDataContext.Operations.Any(x => x.Code.Equals(operation.Code) && !x.Id.Equals(existingId)))
        {
            errorResult = this.GetResponseObject(
                success: 0,
                statusCode: 423,
                messages: new string[] { _localizer["the_code_is_already_used_for_another_operation"] }
            );
            return false;
        }


        // Validate required fields.
        var operationSetting = _appSettingService.Get().OperationSetting;

        ValidateRequiredField.Validate(
            operationSetting,
            OperationSetting.FieldSupplierName,
            operation.SupplierName,
            _localizer["supplier_name_is_required"]
        );
        ValidateRequiredField.Validate(
            operationSetting,
            OperationSetting.FieldMinisterialCode,
            operation.MinisterialCode,
            _localizer["ministerial_code_is_required"]
        );
        ValidateRequiredField.Validate(
            operationSetting,
            OperationSetting.FieldLocalCode,
            operation.LocalCode,
            _localizer["local_code_is_required"]
        );
        ValidateRequiredField.Validate(
            operationSetting,
            OperationSetting.FieldSupplierCategory,
            operation.SupplierCategory,
            _localizer["supplier_category_is_required"]
        );

        ValidateRequiredField.Validate(
            operationSetting,
            OperationSetting.FieldSustainabilityImpact,
            operation.SustainabilityImpact,
            _localizer["sustainability_impact_is_required"]
        );

        ValidateRequiredField.Validate(
            operationSetting,
            OperationSetting.FieldPurpose,
            operation.Purpose,
            _localizer["operation_purpose_is_required"]
        );

        ValidateRequiredField.Validate(
            operationSetting,
            OperationSetting.FieldDuration,
            operation.Duration,
            _localizer["operation_duration_is_required"]
        );
        ValidateRequiredField.Validate(
            operationSetting,
            OperationSetting.FieldMainOperationOwner,
            operation.MainOperationOwner,
            _localizer["main_operation_owner_is_required"]
        );
        // Clean
        operation.NameAr = operation.NameAr.Trim();
        operation.NameEn = operation.NameEn?.Trim() ?? operation.NameAr;
        operation.DescriptionAr = operation.DescriptionAr.Trim();
        operation.DescriptionEn = operation.DescriptionEn?.Trim() ?? operation.DescriptionAr;

        return true;
    }

    private void RemoveRelations(Guid id)
    {
        Utils.RemoveRelations(id, _appDataContext);

        // Level 4 relations.
        _appDataContext.RemoveRange(
            _appDataContext.OperationExecutors.Where(x => x.OperationId.Equals(id)));
        _appDataContext.RemoveRange(
            _appDataContext.OperationFormFiles.Where(x => x.OperationId.Equals(id)));

        _appDataContext.RemoveRange(
            _appDataContext.OperationRuleAndRegulationLinks.Where(x => x.OperationId.Equals(id)));
        _appDataContext.RemoveRange(
            _appDataContext.OperationSpecificationLinks.Where(x => x.OperationId.Equals(id)));
        _appDataContext.RemoveRange(
            _appDataContext.OperationPolicyLinks.Where(x => x.OperationId.Equals(id)));
        _appDataContext.RemoveRange(
            _appDataContext.OperationServiceLinks.Where(x => x.OperationId.Equals(id)));
    }
}
