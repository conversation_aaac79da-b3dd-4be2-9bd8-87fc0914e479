@using Injaz.ModuleEmail.Views.Shared
@model Injaz.ModuleEmail.Core.Commands.SendKpiResultDataEntryResponseApprovalTransferredEmailCommand

@section ContentAr
{
    @await Html.PartialAsync("Spacing", 50)

    <!-- TITLE (AR) -->
    @await Html.PartialAsync("Heading1", new Heading1ViewModel("مرحبا!", true))

    @await Html.PartialAsync("Spacing", 20)

    <!-- MESSAGE (AR) -->
    @await Html.PartialAsync("Heading2", new Heading2ViewModel($"لقد تم تحويل طلب اعتماد ادخال نتائج مؤشر الأداء \"{Model.KpiNameAr}\" لسنة {Model.Year}", true))

    @await Html.PartialAsync("Spacing", 30)

    <!-- GO TO PLAN BUTTON (AR) -->
    @await Html.PartialAsync("Button", new ButtonViewModel("الذهاب للطلب", $"{{{{app_url}}}}/kpi/data-entry-response/detail/{Model.ResponseId}"))

    @await Html.PartialAsync("Spacing", 100)
}

@section ContentEn
{
    @await Html.PartialAsync("Spacing", 50)

    <!-- TITLE (AR) -->
    @await Html.PartialAsync("Heading1", new Heading1ViewModel("Hello!", false))

    @await Html.PartialAsync("Spacing", 20)

    <!-- MESSAGE (AR) -->
    @await Html.PartialAsync("Heading2", new Heading2ViewModel($"The request for approving data entry results for kpi \"{Model.KpiNameEn}\" for year {Model.Year} has been transferred.", false))

    @await Html.PartialAsync("Spacing", 50)

    <!-- GO TO PLAN BUTTON (AR) -->
    @await Html.PartialAsync("Button", new ButtonViewModel("Go to plan", $"{{{{app_url}}}}/kpi/data-entry-response/detail/{Model.ResponseId}"))

    @await Html.PartialAsync("Spacing", 100)
}
