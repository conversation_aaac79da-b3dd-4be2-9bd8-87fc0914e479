@using Injaz.ModuleEmail.Views.Shared
@model Injaz.ModuleEmail.Core.Commands.SendPlanSubsubtaskAwaitingFinalApprovalEmailCommand

@section ContentAr
{
    @await Html.PartialAsync("Spacing", 50)

    <!-- TITLE (AR) -->
    @await Html.PartialAsync("Heading1", new Heading1ViewModel("مرحبا!", true))

    @await Html.PartialAsync("Spacing", 20)

    <!-- MESSAGE (AR) -->
    @await Html.PartialAsync("Heading2", new Heading2ViewModel($"في انتظار الاعتماد النهائي للإجراء {Model.PlanSubtaskNameAr}.", true))

    @await Html.PartialAsync("Spacing", 30)

    <!-- GO TO PLAN BUTTON (AR) -->
    @await Html.PartialAsync("Button", new ButtonViewModel("الذهاب للإجراء", $"{{{{app_url}}}}/plan-subtask/detail/{Model.PlanSubtaskId}"))

    @await Html.PartialAsync("Spacing", 100)
}

@section ContentEn
{
    @await Html.PartialAsync("Spacing", 50)

    <!-- TITLE (AR) -->
    @await Html.PartialAsync("Heading1", new Heading1ViewModel("Hello!", false))

    @await Html.PartialAsync("Spacing", 20)

    <!-- MESSAGE (AR) -->
    @await Html.PartialAsync("Heading2", new Heading2ViewModel($"The plan subtask \"{Model.PlanSubtaskNameEn}\" is final awaiting your approval.", false))

    @await Html.PartialAsync("Spacing", 50)

    <!-- GO TO PLAN BUTTON (AR) -->
    @await Html.PartialAsync("Button", new ButtonViewModel("Go to plan subtask", $"{{{{app_url}}}}/plan-task/detail/{Model.PlanSubtaskId}"))

    @await Html.PartialAsync("Spacing", 100)
}
