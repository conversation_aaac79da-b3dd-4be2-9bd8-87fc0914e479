using Injaz.Core.Models.DomainClasses.App;
using Injaz.ModuleEmail.Abstraction;

namespace Injaz.ModuleEmail.Implementation;

public class Queue : IQueue
{
    private readonly IRepository _repo;

    public Queue(
        IRepository repo
    )
    {
        _repo = repo;
    }

    public void Enqueue(IEnumerable<Email> items)
    {
        _repo.AddList(items);
        _repo.Commit();
    }

    public IEnumerable<Email> Dequeue()
    {
        var items = _repo.GetToBeSent(DequeueLimit).ToList();

        // Lock the emails to prevent double
        // send from another thread.
        items.ForEach(x => x.LockTime = DateTime.UtcNow);

        _repo.Update(items);
        _repo.Commit();

        return items;
    }

    public void FlagSent(IEnumerable<Email> items)
    {
        items = items.ToList();
        foreach (var item in items)
        {
           item.SentTime = DateTime.UtcNow;
           item.LockTime = null;
        }
        
        _repo.Update(items);
        _repo.Commit();
    }

    private const int DequeueLimit = 20;
}