using Injaz.Core.Dtos.KpiResultCapability;
using Injaz.Core.Permission;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MNMWebApp.Binders;
using MNMWebApp.Controllers;

namespace Injaz.ModuleKpiResultCapability.Controllers;

public partial class KpiResultCapabilityController
{
    [HttpGet]
    [Route("/kpi-result-capability")]
    [Authorize(Policy = PermissionNameList.KpiResultRead)]
    public async Task<IActionResult> List(
        Guid periodId
    )
    {
        return this.GetResponseObject(extra: await _kpiResultCapabilityService.ListByResultPeriodAsync(periodId));
    }

    [HttpGet]
    [Route("/kpi-result-capability/{id:guid}")]
    [Authorize(Policy = PermissionNameList.KpiResultRead)]
    public async Task<IActionResult> Get(
        Guid id,
        bool forEdit = false
    )
    {
        var result = await _kpiResultCapabilityService.GetAsync(id, forEdit);

        if (!result.Success)
        {
            return this.GetResponseObject(success: 0, statusCode: 423, messages: new[] { result.Error });
        }

        return this.GetResponseObject(extra: result.Item);
    }

    [HttpPost]
    [Route("/kpi-result-capability")]
    [Authorize(Policy = PermissionNameList.KpiResultEntry)]
    public async Task<IActionResult> Create(
        [BindBodySingleJson] KpiResultCapabilityCreateDto capability,
        Guid periodId
    )
    {
        return await CreateOrUpdateAsync(capability, periodId);
    }

    [HttpPut]
    [Route("/kpi-result-capability")]
    [Authorize(Policy = PermissionNameList.KpiResultEntry)]
    public async Task<IActionResult> Update(
        [BindBodySingleJson] KpiResultCapabilityEditDto capability
    )
    {
        return await CreateOrUpdateAsync(capability);
    }

    [HttpDelete]
    [Route("/kpi-result-capability/{id:guid}")]
    [Authorize(Policy = PermissionNameList.KpiResultDelete)]
    public async Task<IActionResult> Delete(
        Guid id
    )
    {
        var result = await _kpiResultCapabilityService.DeleteAsync(id);

        if (!result.Success)
        {
            return this.GetResponseObject(success: 0, statusCode: 423, messages: new[] { result.Error });
        }

        return this.GetResponseObject(messages: new[] { "item_removed_successfully" });
    }

    private async Task<IActionResult> CreateOrUpdateAsync(KpiResultCapabilityCreateDto capability, Guid? periodId = null)
    {
        var result = await _kpiResultCapabilityService.CreateOrUpdateForResultAsync(capability, periodId);

        if (!result.Success)
        {
            return this.GetResponseObject(success: 0, statusCode: 423, messages: result.Errors);
        }

        return this.GetResponseObject(extra: result.Item);
    }
}
