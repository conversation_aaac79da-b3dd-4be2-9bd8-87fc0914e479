using Injaz.Core.Permission;
using Injaz.ModuleDepartment.Core.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using MNMWebApp.Controllers;

namespace Injaz.ModuleDepartment.Controllers;

public class OperationController: Controller
{
    private readonly IMediator _mediator;

    public OperationController(
        IMediator mediator
    )
    {
        _mediator = mediator;
    }

    [HttpGet]
    [Route("/department/{id:guid}/owned-operation")]
    [Authorize(Policy = PermissionNameList.DepartmentRead)]
    public async Task<IActionResult> DepartmentOwnedOperations(
        Guid id,
        string keyword = "",
        int pageNumber = 0,
        int pageSize = 20)
    {
        return this.GetResponseObject(extra: await _mediator.Send(new GetDepartmentOperationListQuery
        {
            Id = id,
            Keyword = keyword,
            PageNumber = pageNumber,
            PageSize = pageSize
        }));
    }
}
