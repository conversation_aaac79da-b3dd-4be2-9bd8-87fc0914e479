using Injaz.Core;
using Injaz.Core.Dtos.Kpi;
using Injaz.Core.Dtos.Misc;
using MediatR;
using LinqKit;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.ModuleDepartment.Core.Queries;

namespace Injaz.ModuleDepartment.Handlers;

public class GetOwnedKpiListHandler : IRequestHandler<GetDepartmentOwnedKpiListByQuery, TableResultDto<KpiSimpleDto>>
{
    private readonly DbContext _db;

    public GetOwnedKpiListHandler(
        DbContext db
    )
    {
        _db = db;
    }

    public Task<TableResultDto<KpiSimpleDto>> Handle(GetDepartmentOwnedKpiListByQuery request,
        CancellationToken cancellationToken)
    {
        request.Keyword = request.Keyword?.ToLower().Trim() ?? "";

        var lang = HelperFunctions.GetLanguageCode();
        var ownedKpiExpression = KpiSimpleDto.Mapper(lang);

        var items = _db
            .Departments
            .AsExpandable()
            .Where(x => x.Id == request.Id)
            .SelectMany(x => x.OwnedKpis)
            .Where(x => x.Status.Equals(Kpi.StatusActive));

        var filteredItems = items
            .Where(x => x.NameAr.Contains(request.Keyword) || x.NameEn.ToLower().Contains(request.Keyword))
            .Select(x => ownedKpiExpression.Invoke(x));

        return Task.FromResult(new TableResultDto<KpiSimpleDto>()
        {
            Items = filteredItems
                .OrderBy(x => x.Name)
                .Skip(request.PageSize * request.PageNumber)
                .Take(request.PageSize)
                .ToList(),
            Count = items.Count(),
            FilteredCount = filteredItems.Count()
        });
    }
}
