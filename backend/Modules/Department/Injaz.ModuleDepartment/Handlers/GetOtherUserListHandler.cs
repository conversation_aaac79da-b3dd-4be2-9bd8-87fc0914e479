using Injaz.Core;
using Injaz.Core.Dtos.Misc;
using Injaz.Core.Dtos.User;
using Injaz.ModuleDepartment.Core.Queries;
using MediatR;

namespace Injaz.ModuleDepartment.Handlers;

public class
    GetOtherUserListHandler : IRequestHandler<GetDepartmentOtherUserListQuery,
        TableResultDto<UserWithEmployeeNumberDto>>
{
    private readonly DbContext _db;

    public GetOtherUserListHandler(
        DbContext db
    )
    {
        _db = db;
    }

    public Task<TableResultDto<UserWithEmployeeNumberDto>> Handle(GetDepartmentOtherUserListQuery request,
        CancellationToken cancellationToken)
    {
        request.Keyword = request.Keyword?.ToLower().Trim() ?? "";
        request.EmployeeNumber = request.EmployeeNumber?.ToLower().Trim() ?? "";

        var existingUsers = _db
            .DepartmentUserLinks
            .Where(x => x.DepartmentId.Equals(request.Id))
            .Select(x => x.UserId)
            .ToArray();

        var items = _db
            .Users
            .Where(x => x.Status != Injaz.Core.Models.DomainClasses.App.User.StatusDeleted)
            .Where(x => !existingUsers.Contains(x.Id));

        var filteredItems =
            items
                .Where(x => x.NameAr.Contains(request.Keyword) || x.NameEn.ToLower().Contains(request.Keyword))
                .Where(x => x.EmployeeNumber.ToLower().Contains(request.EmployeeNumber));


        return Task.FromResult(new TableResultDto<UserWithEmployeeNumberDto>
        {
            Items = filteredItems
                .Select(UserWithEmployeeNumberDto.Mapper(HelperFunctions.GetLanguageCode()))
                .OrderBy(x => x.Name)
                .Skip(request.PageNumber * request.PageSize)
                .Take(request.PageSize),
            Count = _db.Users.Count(),
            FilteredCount = items.Count()
        });
    }
}
