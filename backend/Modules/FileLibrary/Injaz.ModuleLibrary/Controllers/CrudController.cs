using Injaz.Core.Extensions;
using Injaz.Core.Models.DomainClasses.App;
using Injaz.Core.Permission;
using Injaz.Core.Resources.Shared;
using Injaz.ModuleLibrary.Core.Commands;
using Injaz.ModuleLibrary.Core.Constants;
using Injaz.ModuleLibrary.Core.Queries;
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Localization;
using MNMWebApp.Binders;
using MNMWebApp.Controllers;

namespace Injaz.ModuleLibrary.Controllers;

[Authorize]
public class CrudController : Controller
{
    private readonly UserManager<User> _userManager;
    private readonly IMediator _mediator;
    private readonly IStringLocalizer<SharedResource> _localizer;

    public CrudController(
        IMediator mediator,
        IStringLocalizer<SharedResource> localizer,
        UserManager<User> userManager
    )
    {
        _userManager = userManager;
        _mediator = mediator;
        _localizer = localizer;
    }

    [HttpGet]
    [Route("/library-file")]
    public async Task<IActionResult> List(
        string keyword,
        bool onlyMyFiles = false,
        Guid[] tagIds = null,
        string orderBy = null,
        int pageNumber = 0,
        int pageSize = 20,
        Guid[]? ownerIds = null,
        string[]? contentTypes = null,
        Guid[]? excludeIds=null
    )
    {
        var userId = Guid.Parse(_userManager.GetUserId(User));
        return this.GetResponseObject(extra: await _mediator.Send(new GetLibraryFileListQuery
        {
            Keyword = keyword,
            OnlyMyFiles = onlyMyFiles,
            TagIds = tagIds,
            OrderBy = orderBy,
            PageNumber = pageNumber,
            PageSize = pageSize,
            Scopes = new[] { LibraryFileScopes.Shared },
            CurrentUserId = userId,
            OwnerIds = ownerIds,
            ContentTypes = contentTypes,
            ExcludeIds = excludeIds
        }));
    }

    [HttpGet]
    [Route("/library-file/{id}")]
    public async Task<IActionResult> Get(
        Guid id,
        bool forEdit = false
    )
    {
        var item = forEdit
            ? await _mediator.Send(new GetLibraryFileForEditByIdQuery
                { Id = id, Scopes = new[] { LibraryFileScopes.Shared } }) as object
            : await _mediator.Send(new GetLibraryFileByIdQuery
                { Id = id, Scopes = new[] { LibraryFileScopes.Shared } });

        return this.GetResponseObject(extra: item);
    }

    [HttpPost]
    [Route("/library-file")]
    public async Task<IActionResult> Create(
        [BindBodySingleJson] CreateLibraryFileCommand libraryFile
    )
    {
        libraryFile.CurrentUserId = Guid.Parse(_userManager.GetUserId(User));
        return this.GetResponseObject(
            extra: await _mediator.Send(libraryFile)
        );
    }

    [HttpPut]
    [Route("/library-file")]
    public async Task<IActionResult> Update(
        [BindBodySingleJson] UpdateLibraryFileCommand libraryFile
    )
    {
        return this.GetResponseObject(
            extra: await _mediator.Send(libraryFile)
        );
    }

    [HttpDelete]
    [Route("/library-file/{id:guid}")]
    [Authorize(Policy = PermissionNameList.FullAccess)]
    public async Task<IActionResult> Delete(
        Guid id
    )
    {
        await _mediator.Send(new DeleteLibraryFileCommand
        {
            Id = id
        });

        return this.GetResponseObject(
            messages: new string[] { _localizer["operation_successful"] }
        );
    }


    [HttpGet]
    [Route("/library-file/download/{id:guid}")]
    public async Task<IActionResult> Download(Guid id)
    {
        var fileContent = await _mediator.Send(new GetLibraryFileStreamQuery
        {
            Id = id,
            CurrentUserHasPlanReadPermission = await this.EnsureUserHasPermission(PermissionNameList.PlanRead)
        });

        return File(fileContent.Stream, fileContent.ContentType);
    }

    [HttpGet]
    [Route("/library-file/linked-resource/{id:guid}")]
    public async Task<IActionResult> LinkedResources(Guid id)
    {
        return this.GetResponseObject(extra:
            await _mediator.Send(new GetLibraryFileLinkedResourceListQuery { Id = id }));
    }

    [HttpPut]
    [Route("/library-file/linked-resource/update")]
    public async Task<IActionResult> LibraryResources(
        [BindBodySingleJson] UpdateLibraryResourcesCommand libraryResources
    )
    {
        return this.GetResponseObject(
            extra: await _mediator.Send(libraryResources)
        );
    }
}
